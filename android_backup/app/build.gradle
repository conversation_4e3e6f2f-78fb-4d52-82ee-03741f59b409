plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
    id "com.google.firebase.crashlytics"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

// Plugins are now declared in the plugins block above

project.logger.lifecycle("Flutter SDK root: $flutterRoot; rootProject: $rootProject; localPropertiesFile: $localPropertiesFile")

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
    project.logger.lifecycle("keystorePropertiesFile: $keystorePropertiesFile; keystoreProperties storeFile: ${keystoreProperties['storeFile']}")

    def storeFileToLog = rootProject.file(keystoreProperties['storeFile'])
    project.logger.lifecycle("storeFileToLog: $storeFileToLog; storeFileToLog: ${storeFileToLog.exists()}")
}

android {
    namespace "io.x1440"
    compileSdkVersion 35
    buildToolsVersion "30.0.3"
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "io.x1440"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 26
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
        ndk {
            debugSymbolLevel 'FULL'
        }
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
            shrinkResources false
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "androidx.multidex:multidex:2.0.1"
    implementation(platform("com.google.firebase:firebase-bom:33.9.0"))
    implementation 'com.google.firebase:firebase-messaging'
    implementation 'com.google.code.gson:gson:2.11.0'
    implementation "androidx.security:security-crypto:1.1.0-alpha06"
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.10.1'
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    implementation "androidx.work:work-runtime-ktx:2.10.0"
}
