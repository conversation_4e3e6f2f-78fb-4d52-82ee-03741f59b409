fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

### usage

```sh
[bundle exec] fastlane usage
```

Push a new release build to the App Store

### apply_version

```sh
[bundle exec] fastlane apply_version
```



### test

```sh
[bundle exec] fastlane test
```



### release

```sh
[bundle exec] fastlane release
```



----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
