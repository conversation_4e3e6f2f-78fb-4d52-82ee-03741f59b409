# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

def latest_googleplay_version_code
  productionVersionCodes = google_play_track_version_codes(track: 'production')
  betaVersionCodes = google_play_track_version_codes(track: 'beta')
  alphaVersionCodes = google_play_track_version_codes(track: 'alpha')
  internalVersionCodes = google_play_track_version_codes(track: 'internal')

  # puts version codes from all tracks into the same array
  versionCodes = [
    productionVersionCodes,
    betaVersionCodes,
    alphaVersionCodes,
    internalVersionCodes
  ].reduce([], :concat)

  # returns the highest version code from array
  return versionCodes.max
end

lane :get_app_store_build_number do ### NOTE: this isn't fully working
    ENV["LATEST_GOOGLE_PLAY_VERSION"] = latest_googleplay_version_code
end

lane :playstore do
  Dir.chdir("..") do
    sh "flutter build appbundle"
    sh "pwd" # /home/<USER>/project/android
  end
  upload_to_play_store(
    track:"internal", # production, beta, alpha, internal
    aab: '../build/app/outputs/bundle/release/app-release.aab',
   )
end