# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
#update_fastlane
require 'json'

desc "Push a new release build to the App Store"
before_all do
  ENV['APP_NAME'] = '1440'
  ENV["APP_PACKAGE"] = 'io.1440'
  ENV["APP_NOTIFICATION_EXTENSION_PACKAGE"] = 'io.1440.NotificationsServiceExtension'
  ENV["APP_LIVEACTIVITIES_EXTENSION_PACKAGE"] = 'io.1440.messageLiveActivity'
  ENV["FIREBASE_TEST_TEAM"] = 'internal'
  ENV["ANDROID_STORE_TRACK"] = 'internal'
  ENV['FIREBASE_DISTRIBUTION'] = '1:638828498963:android:40967f9f4ab7e753a32fe6'
 
end

lane :usage do
  puts '============================================================================================================================'
  puts 'A build runs onto the release lane.'
  puts ''
  puts 'The branch and platform parameters are required.'
  puts '- platform can be: ios, android. For example: platform:ios'
  puts '- branch must be passed. For example: branch:dev'
  puts '- build is optional. For example: build:256'
  puts '- flavor is optional and defaults to prod. For example: flavor:prod flavor:prod,staging or flavor:prod,staging,dev'
  puts ''
  puts 'Builds would be something like:'
  puts 'bundle exec fastlane release branch:develop platform:ios falvor:staging'
  puts 'bundle exec fastlane release branch:release/1.2.0 platform:android'
  puts 'bundle exec fastlane release branch:develop platform:ios'
  puts 'bundle exec fastlane release branch:develop platform:web'
  puts '============================================================================================================================'

end

lane :apply_version do
  set_version("../../pubspec.yaml", "../../version.yaml")
end

lane :test do 
  begin
    sh("pwd")

    generate_flutter("../..")

    Dir.chdir ("../..") do
      #sh("flutter test") # TBD: Run test on the consumer side
    end
  rescue => exception
    slack_error(exception)
  end
end

lane :release do |options|
  begin
    
    flavors = validate_parameters_and_environment(options)
      
    current_branch = sh("git branch --show-current").strip

    if options[:branch] != current_branch
      raise "Branch mismatch: expected '#{options[:branch]}', but found '#{current_branch}'"
    end

    # send_binary_artifact("staging", 1, "ios", '1234', options[:branch])
    # exit
    pre_build_actions(options)
      
    environments = get_app_environments(options[:branch])
    app_identifiers = environments[1]
    environment = environments[2]
    android_ids = environments[3]
    pr_number = options[:pr]

    release = get_release_number(options[:branch])
    puts "Release: #{release}"
    puts "Build for Appium: #{ENV["APPIUM"]}"

    

    flavors.each do |flavor|
      puts "Building for environment: #{flavor}"
      store_build_number = -1
      clean_build(flavor)

      # if a version is passed (for example to force a specific version number) use it, 
      # otherwise get the next build number from the store
      if !options[:version].nil? && !options[:version].empty? 
        store_build_number = options[:version]
      else
        store_build_number = get_next_build_number(app_identifiers[flavor], release, android_ids[flavor])
      end
      
      puts "Store Build Number for flavor #{flavor}: #{store_build_number}"

      build_flutter(release,store_build_number,flavor,ENV["PLATFORM"]) 
      build_binary(flavor, "app-store")

      if ENV["APPIUM"].to_s.downcase != 'true'
        case ENV["PLATFORM"]
        when "ios"
          upload_binary_to_store(flavor, "app-store") 
          upload_dsyms(flavor)
        when "android"
        upload_binary_to_store(flavor, release, store_build_number) 
        end

        slack_success(flavor, store_build_number, ENV["PLATFORM"], release, options[:branch], pr_number)
      end
    end
    restore_appfiles_matchfiles
  rescue => exception
    restore_appfiles_matchfiles
    slack_error(exception)
  end
end

desc "checkout appfiles and matcfile"
def restore_appfiles_matchfiles
  Dir.chdir ("../..") do
    sh "git checkout ios/fastlane/Appfile"
    sh "git checkout ios/fastlane/Matchfile"
    sh "git checkout android/fastlane/Appfile"
    sh "rm -f android/playstorejson.json"
  end
end

def send_binary_artifact(flavor, store_build_number, platform, release, branch)
  case ENV["PLATFORM"]
  when "ios"
    # build/ios/iphonesimulator/Runner.ap
    Dir.chdir ("../..") do
    pwd = sh('pwd')
    sh('zip -r Runner.zip build/ios/iphonesimulator/Runner.app')

    # Upload to slack
    slack_upload(
            slack_api_token: "xapp-1-A07M6B3T46S-7720383626966-2c18e4ecd9b1cdb4f79bd19aa3880a3faf57ab54bdff715ebe17737cfa64b3cf", # Preferably configure as ENV['SLACK_API_TOKEN']
            title: "Flutter Appium App Build (#{platform}): (#{flavor}) | Release: #{release} | Branch: #{branch} | Store build: #{store_build_number}",
            channel: "#flutter-builds",
            file_path: "#{pwd.chomp}/Runner.zip",
            initial_comment: "Changelog goes here"
    ) 
      # slack(
      #   message: "Flutter Appium App Build (#{platform}): (#{flavor}) | Release: #{release} | Branch: #{branch} | Store build: #{store_build_number}",
      #   success: true,
      #   file: "Runner.zip",
      #   file_type: "zip",
      #   filename: "Runner_#{flavor}_#{store_build_number}.zip",
      #   payload: {
      #     "Platform" => platform,
      #     "Flavor" => flavor,
      #     "Release" => release,
      #     "Branch" => branch,
      #     "Store build" => store_build_number
      #   },
      #   slack_url: "*******************************************************************************",
      #   default_payloads: [:git_branch, :git_author, :last_git_commit, :last_git_commit_hash]
      # )
    end
  when "android"
    upload_binary_to_store(flavor, build_number) 
  end
end

desc "validate parameters and environment variable"
def validate_parameters_and_environment(options)
  # Validate parameters
  raise 'branch name argument is missing' unless !options[:branch].nil?
  raise 'platform argument is missing' unless !options[:platform].nil? 
  raise 'platform must be either ios or android' unless ['ios', 'android'].include? options[:platform]
  
  ENV["PLATFORM"] = options[:platform]
  ENV["BUILD"] = options[:build] 
  ENV["BRAND"] = options[:brand]
  ENV["APPIUM"] = (options[:appium].to_s.downcase == 'true').to_s

  if options[:flavor].nil? || options[:flavor].empty?
    raise 'flavor is missing. It must be set to: prod, dev or staging'
  end

  if !options[:flavor].nil? && !options[:flavor].empty? 
    #puts options[:flavor]
    flavors = options[:flavor].split(',')
    # Define the allowed values
    allowed_values = ["prod", "staging", "dev"]

    # Check if all elements in flavors are within the allowed values
    if flavors.all? { |flavor| allowed_values.include?(flavor) }
      puts "All flavors are valid."
    else
      puts "There are invalid flavors."
      raise 'flavor must be: prod, dev or staging or a vocombination of any of these'
    end
  end

  # validate required environment variables
  case ENV["PLATFORM"]
  when "ios"
    raise 'APPSTORE_KEY_PATH environment variable is missing' unless ENV["APPSTORE_KEY_PATH"]
    raise 'APPSTORE_KEY_ID environment variable is missing' unless ENV["APPSTORE_KEY_ID"]
  when "android"
    raise 'FIREBASE_TOKEN environment variable missing' unless ENV["FIREBASE_TOKEN"]
    raise 'ANDROID_KEYSTORE environment variable missing' unless ENV['ANDROID_KEYSTORE']
    raise 'ANDROID_JSON_KEY environment variable missing' unless ENV['ANDROID_JSON_KEY']
  end

  return flavors
end

desc "raises the exception and slack it"
def slack_error(exception)
  puts exception
  # slack(
  #   message: "Flutter Consumer App failed with exception : #{exception}",
  #   success: false,
  #   slack_url: "*******************************************************************************",
  #   default_payloads: [:git_branch, :git_author, :last_git_commit, :last_git_commit_hash]
  # )
  raise
end

desc "clean build"
def clean_build(flavor)
  puts 'Cleaning project'
  case ENV["PLATFORM"]
  when "ios"
	  clear_derived_data
	  xcclean(scheme: 'Runner')
    sh "flutter precache"
  when "android"

  end
end

desc "get latest build number from store using the Ruby spalt operator allowing to behave like varargs"
def get_next_build_number(*options)
  
  if ENV["BUILD"]&.empty? == false
    latest_build_number = ENV["BUILD"].to_i
  else
    latest_build_number = -1
    case ENV["PLATFORM"]
    when "ios"
      puts "build number app_identifier: #{options[0]} version: #{options[1]}"
      build_number = latest_testflight_build_number(app_identifier:options[0], version: options[1])
    when "android"
      appId =  options[2]
      puts "appId: #{appId}"
      build_number = firebase_app_distribution_get_latest_release(app: appId, firebase_cli_token: ENV["FIREBASE_TOKEN"]) # it returns a json structure
      puts"latest_build_number: #{build_number}"
      if build_number.nil?
        build_number = 0
      else
        build_number = build_number[:buildVersion]
      end

      # latest_build_number = google_play_track_version_codes(package_name: ENV["ANDROID_PACKAGE"], track: ENV["ANDROID_STORE_TRACK"], json_key: ENV['ANDROID_JSON_KEY']).first
    end
    
    latest_build_number = (build_number.is_a? String) ? build_number.to_i : build_number
    latest_build_number += 1
    puts 'Setting build number to: ' + latest_build_number.to_s
  end
  return latest_build_number
end

desc "return the version number if the branch is release pubspec.yaml release number otherwise"
def get_release_number(branch) 
  version = branch[/\d+\.\d+\.\d+/]

  if version.nil? || version.empty? 
    version = get_flutter_version_from_pubspec()
  end

  return version
end

desc "return the various build configurations"
def get_app_environments(branch)
  # when we push to dev we push dev and stage to testflight. Hotfix or prod, we push dev, stage and prod to testflight
  schemes = branch.start_with?("release","hotfix","develop") ? ["production"] : ["production"]
  # app identifiers as defined in AppstoreConnect
  app_identifiers = { "Runner" => "#{ENV["APP_PACKAGE"]}" }
  environments = { "production" => "production" }
  app_android_firebase_ids = { "prod" => "#{ENV['FIREBASE_DISTRIBUTION']}", "staging" => "#{ENV['FIREBASE_DISTRIBUTION']}", "dev" => "#{ENV['FIREBASE_DISTRIBUTION']}" }

  raise 'iOS branch ' + branch + ' unknown' unless !schemes.empty?

  return [schemes, app_identifiers, environments, app_android_firebase_ids]
end

desc "pre-build actions"
def pre_build_actions(options, *params)  
  sh 'bundle install'
  sh 'flutter pub cache clean -f'
  sh 'dart pub global activate flutterfire_cli'

  case ENV["PLATFORM"]
  when "ios"
    #sh 'rm -fr Podfile.lock'
    #sh 'rm -fr Pods'
    update_appfile()
    update_matchfile()
    puts "KEYID: #{ENV["APPSTORE_KEY_ID"]}"
    puts "ISSUER: #{ENV["IOS_ISSUER_ID"]}"
    puts "PATH: #{ENV["APPSTORE_KEY_PATH"]}"

    key = app_store_connect_api_key(
      key_id: ENV["APPSTORE_KEY_ID"],
      issuer_id:  ENV["IOS_ISSUER_ID"],
      key_filepath: ENV["APPSTORE_KEY_PATH"]
    ) 
    puts "APP STORE CONNECT API KEY: #{key}"
  when "android"
    update_appfile()
    Dir.chdir ("..") do
      sh ('pwd')
      sh 'cp $ANDROID_JSON_KEY .'
      sh 'rm -fr app/keystore'
      sh 'mkdir app/keystore'
      sh 'cp $KEY_PROPERTIES .'
      sh 'cp -R $ANDROID_KEYSTORE/key.jks app/keystore/'
    end
  end

  generate_flutter("../..")
 
  case ENV["PLATFORM"]
  when "ios"
    cocoapods(
      repo_update: true
    )
    patch_pods_framework
  end

end


desc "Run pub get, automatic code generation (id needed)"
def generate_flutter(directory, code_gen=true)
  Dir.chdir (directory) do
    sh "rm -f pubspec.lock"
    sh "flutter pub get"
    if code_gen 
      sh "flutter packages pub run build_runner build --delete-conflicting-outputs"
    end
  end
end

desc "build flutter app"
def build_flutter(version,build_number,flavor, platform) 

  Dir.chdir ("../..") do
    case ENV["PLATFORM"]
    when "ios"  #should we add --obfuscate? It fails when we upload dsyms... Error (Xcode) with --obfuscate enabled. Crashlytics does not support uploading obfuscated dSYMs via the command line. Please upload dSYMs through the Firebase console web uploader at https://firebase.corp.google.com/project/_/crashlytics
      command = "flutter build ipa --release --no-codesign --build-name=#{version} --build-number=#{build_number} --dart-define=env=#{flavor} -t lib/main.dart --no-tree-shake-icons"
      if ENV["APPIUM"].to_s.downcase == 'true'
        command += " --dart-define=integration_test=true"
        command = command.gsub("--release", "--simulator").gsub("ipa", "ios").gsub("--no-codesign", "")
      end
      sh command
      when "android"
        brand = ENV["BRAND"]
        
      case flavor
      # when "production" TODO: Fix this later
      #   sh "flutter build appbundle --target-platform android-arm,android-arm64,android-x64 --release --flavor=#{flavor} --build-name=#{version} --build-number=#{build_number} -t lib/main.dart --dart-define=env=#{flavor} --obfuscate --split-debug-info=/tmp"
      when "prod"
        command = "flutter build appbundle --target-platform android-arm,android-arm64,android-x64 --release  --build-name=#{version} --build-number=#{build_number} --dart-define=env=#{flavor} -t lib/main.dart --no-tree-shake-icons"
        if !brand.nil?
          branding_define = " --dart-define=brand=#{brand}"
          command += branding_define
        end
        
        sh command
      end

      if !brand.nil?
        branding_define = " --dart-define=brand=#{brand}"
        command += branding_define
      end
      command = "flutter build apk --target-platform android-arm,android-arm64,android-x64 --debug  --build-name=#{version} --build-number=#{build_number} --dart-define=env=#{flavor} -t lib/main.dart  --no-tree-shake-icons"
      if ENV["APPIUM"].to_s.downcase == 'true'
        command += " --dart-define=integration_test=true"
      end
      sh command
    end
  end
end

desc "Extract version number from pubspec.yaml"
def get_flutter_version_from_pubspec
  File.open('../../pubspec.yaml', 'r') do |file|
    # Read each line of the file
    file.each_line do |line|
      # Extract the version number if it exists on the line
      if line.match(/version: (\d+\.\d+\.\d+)/)
        version = $1  # $1 contains the matched version number
        puts "Found version: #{version}"
        return version
      end
    end
  end
end

desc "Tweak pod framework file to archive on XCode 14.3"
def patch_pods_framework
  Dir.chdir ("..") do
    pwd = sh('pwd')
    filename = "#{pwd.chomp}/Pods/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh"
    outdata = File.read(filename).gsub('source="$(readlink', 'source="$(readlink -f')

    File.open(filename, 'w') do |out|
      out << outdata
    end 
  end
end

desc "Slack success message"
def slack_success(flavor, store_build_number, platform, release, branch, pr_number)
  if !pr_number.nil?
    pr_data = sh("gh pr view #{pr_number} --json commits").strip
    pr_data = JSON.parse(pr_data)
    pr_data['commits'].each do |commit|
      commit.delete('oid')
      commit.delete('authors')
      commit.delete("authoredDate")
      commit.delete("messageBody")
    end
    pr_number = pr_number + ": " + JSON.pretty_generate(pr_data)
  else
    pr_number = "No PR provided"
  end

  slack(
    message: "Flutter App Build (#{platform}): (#{flavor}) | Release: #{release} | Branch: #{branch} | Store build: #{store_build_number}",
    success: true,
    payload: {
      "Platform" => platform,
      "Flavor" => flavor,
      "Release" => release,
      "Branch" => branch,
      "Store build" => store_build_number,
      "PR" => pr_number
    },
    slack_url: "*******************************************************************************",
    default_payloads: [:git_branch, :git_author, :last_git_commit, :last_git_commit_hash]
  )
end

desc "build binary"
def build_binary(flavor, export_method="app-store")
    case ENV["PLATFORM"]
    when "ios"
      # provisioning profiles for app and extension(s)
      provisioning_profiles = { 
        "prod" => [
          ["#{ENV["APP_PACKAGE"]}","match AppStore #{ENV["APP_PACKAGE"]}"],
          ["#{ENV["APP_NOTIFICATION_EXTENSION_PACKAGE"]}","match AppStore #{ENV["APP_NOTIFICATION_EXTENSION_PACKAGE"]}"],
          ["#{ENV["APP_LIVEACTIVITIES_EXTENSION_PACKAGE"]}","match AppStore #{ENV["APP_LIVEACTIVITIES_EXTENSION_PACKAGE"]}"]
        ],
        "staging" => [
          ["#{ENV["APP_PACKAGE"]}","match AppStore #{ENV["APP_PACKAGE"]}"],
          ["#{ENV["APP_NOTIFICATION_EXTENSION_PACKAGE"]}","match AppStore #{ENV["APP_NOTIFICATION_EXTENSION_PACKAGE"]}"],
          ["#{ENV["APP_LIVEACTIVITIES_EXTENSION_PACKAGE"]}","match AppStore #{ENV["APP_LIVEACTIVITIES_EXTENSION_PACKAGE"]}"]
        ],
        "dev" => [
          ["#{ENV["APP_PACKAGE"]}","match AppStore #{ENV["APP_PACKAGE"]}"],
          ["#{ENV["APP_NOTIFICATION_EXTENSION_PACKAGE"]}","match AppStore #{ENV["APP_NOTIFICATION_EXTENSION_PACKAGE"]}"],
          ["#{ENV["APP_LIVEACTIVITIES_EXTENSION_PACKAGE"]}","match AppStore #{ENV["APP_LIVEACTIVITIES_EXTENSION_PACKAGE"]}"]
        ]
      }

      update_code_signing_settings(
        use_automatic_signing: false,
        team_id: ENV["IOS_TEAM_ID"]
      )

      match(
        type: "appstore",
        readonly: true,
        app_identifier: provisioning_profiles[flavor].map { |entries| entries[0] }.join(',')
        #app_identifier: "#{provisioning_profiles[flavor][0][0]},#{provisioning_profiles[flavor][1][0]},#{provisioning_profiles[flavor][2][0]}",
      )

      update_project_provisioning(
        xcodeproj: "Runner.xcodeproj",
        target_filter: "Runner",
        profile:ENV["sigh_#{provisioning_profiles[flavor][0][0]}_appstore_profile-path"],
        build_configuration: "Release"
      )

      update_project_provisioning(
        xcodeproj: "Runner.xcodeproj",
        target_filter: "1440NotificationServiceExtension",
        profile:ENV["sigh_#{provisioning_profiles[flavor][1][0]}_appstore_profile-path"],
        build_configuration: "Release"
      )

      update_project_provisioning(
        xcodeproj: "Runner.xcodeproj",
        target_filter: "messageLiveActivityExtension",
        profile:ENV["sigh_#{provisioning_profiles[flavor][2][0]}_appstore_profile-path"],
        build_configuration: "Release"
      )
      
      puts("Provisioning profiles (1/3): #{provisioning_profiles[flavor][0][0]} -> #{ENV["sigh_#{provisioning_profiles[flavor][0][0]}_appstore_profile-name"]}")
      puts("Provisioning profiles (2/3): #{provisioning_profiles[flavor][1][0]} -> #{ENV["sigh_#{provisioning_profiles[flavor][1][0]}_appstore_profile-name"]}")
      puts("Provisioning profiles (3/3): #{provisioning_profiles[flavor][2][0]} -> #{ENV["sigh_#{provisioning_profiles[flavor][2][0]}_appstore_profile-name"]}")

      case export_method
      when "app-store"
        destination = "generic/platform=iOS"
        build_app(
          workspace: "Runner.xcworkspace", 
          buildlog_path: "/tmp", 
          scheme: 'Runner', 
          configuration: "Release",
          xcargs: "-allowProvisioningUpdates",
          destination: destination,
          export_method: export_method,
          codesigning_identity: ENV["sigh_#{provisioning_profiles[flavor][0][0]}_appstore_certificate-name"],
          export_options: {
            provisioningProfiles: {
              provisioning_profiles[flavor][0][0] => ENV["sigh_#{provisioning_profiles[flavor][0][0]}_appstore_profile-name"],
              provisioning_profiles[flavor][1][0] => ENV["sigh_#{provisioning_profiles[flavor][1][0]}_appstore_profile-name"],
              provisioning_profiles[flavor][2][0] => ENV["sigh_#{provisioning_profiles[flavor][2][0]}_appstore_profile-name"]
            }
          }
        )
        end
  when "android"
    puts "android binary build done"
  end
  
end

desc "Upload to the appropriate store"
def upload_binary_to_store(flavor, *options) 
  case ENV["PLATFORM"]
  when "ios"
      build_type = options[0]
      case build_type
      when "app-store"
        path_to_ipa = ""
        upload_to_testflight(
          skip_waiting_for_build_processing: true,
          wait_processing_interval: 1,
          skip_submission: true
        )
      end
  when "android"
    path_to_aab = ""
    path_to_apk = ""

    Dir.chdir ("../..") do
      pwd = sh('pwd')
      path_to_aab = "#{pwd.chomp}/build/app/outputs/bundle/Release/app-release.aab"
      path_to_apk = "#{pwd.chomp}/build/app/outputs/apk/debug/app-debug.apk"
    end

    case flavor
    when "prod"
        upload_to_play_store(track:ENV["ANDROID_STORE_TRACK"], skip_upload_apk: true, release_status: 'draft', aab: path_to_aab)
    end
    
    upload_to_firebase("APK", path_to_apk, flavor, options[0], options[1], ENV["FIREBASE_TEST_TEAM"])

  end
end

def upload_to_firebase(binary_type, binary, track, version, build, teams)
  puts "PATH TO Binary: #{binary}"
  puts "Binary Type: #{binary_type}"
  puts "TEAMS: #{teams}"
  puts 'CANNOT FIND BINARY PATH' unless File.exist?(binary)
  
  case ENV["PLATFORM"]
  when "android"
    firebase_app_distribution(
      app: track == 'production' ? "#{ENV['FIREBASE_DISTRIBUTION']}" : "#{ENV['FIREBASE_DISTRIBUTION']}", # we have only one app for now
      groups: teams,
      release_notes: "#{ENV['APP_NAME'].capitalize} Flutter - #{track}",
      firebase_cli_token: ENV['FIREBASE_TOKEN'],
      android_artifact_type: "#{binary_type}",
      android_artifact_path: binary
    )
  end
end

desc "upload dynamic symbols for crashlytics"
def upload_dsyms(flavor)
  case ENV["PLATFORM"]
  when "ios"
    Dir.chdir ("..") do
      sh('pwd')
      pwd = sh('pwd')

      sh("#{pwd.chomp}/Pods/FirebaseCrashlytics/upload-symbols -gsp #{pwd.chomp}/Runner/GoogleService-Info.plist -p ios #{pwd.chomp}/Runner.app.dSYM.zip")
    end
  when "android"
    sh('ls -la /tmp')
  end
end

desc "Set the version in pubspec.yaml. Version is uniquely set in the version.yaml file"
def set_version(pubspec_path, version_path)
  v = File.read(version_path)
  spec = v.match(/major: (\d+)\nminor: (\d+)\npatch: (\d+)/).captures

  puts("Setting: Major: #{spec[0]} Minor: #{spec[1]} Patch: #{spec[2]}")
    
  # Open the pubspec and replace version
  outdata = File.read(pubspec_path).gsub(/version: (\d+).(\d+).(\d+)/, "version: #{spec[0]}.#{spec[1]}.#{spec[2]}")
  File.open(pubspec_path, 'w') do |out|
    out << outdata
  end  
end

desc "Set the version in pubspec.yaml. Version is uniquely set in the version.yaml file"
def update_appfile()
  case ENV["PLATFORM"]
  when "android"
    file_content = File.read(ENV["APPFILE_ANDROID_PATH"])
    new_content = file_content.gsub("PATH_TO_MOBILE_KEYS", ENV["APP_KEYS"])

    puts new_content
    # Write the modified content back to the file
    File.open(ENV["APPFILE_ANDROID_PATH"], "w") {|file| file.puts new_content }

  when "ios"
    file_content = File.read(ENV["APPFILE_IOS_PATH"])
    new_content = file_content.gsub("APPLE_DEV_PORTAL_USERNAME", ENV["APPLE_DEV_PORTAL_USERNAME"])

    puts new_content
    # Write the modified content back to the file
    File.open(ENV["APPFILE_IOS_PATH"], "w") {|file| file.puts new_content }
  end
end

desc "Set the appstore email"
def update_matchfile()
  file_content = File.read(ENV["MATCHFILE_PATH"])
  new_content = file_content.gsub("APPLE_DEV_PORTAL_USERNAME", ENV["APPLE_DEV_PORTAL_USERNAME"])

  puts new_content
  # Write the modified content back to the file
  File.open(ENV["MATCHFILE_PATH"], "w") {|file| file.puts new_content }

end

