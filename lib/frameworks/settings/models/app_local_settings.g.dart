// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_local_settings.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetAppLocalSettingsCollection on Isar {
  IsarCollection<AppLocalSettings> get appLocalSettings => this.collection();
}

const AppLocalSettingsSchema = CollectionSchema(
  name: r'AppLocalSettings',
  id: -4119991048692551607,
  properties: {
    r'aiSuggestionsEnabled': PropertySchema(
      id: 0,
      name: r'aiSuggestionsEnabled',
      type: IsarType.bool,
    ),
    r'hasRequestedPushPermission': PropertySchema(
      id: 1,
      name: r'hasRequestedPushPermission',
      type: IsarType.bool,
    ),
    r'selectedEnvironment': PropertySchema(
      id: 2,
      name: r'selectedEnvironment',
      type: IsarType.object,
      target: r'SalesforceEnvironment',
    ),
    r'showDevOptions': PropertySchema(
      id: 3,
      name: r'showDevOptions',
      type: IsarType.bool,
    ),
    r'userRequestsAiSuggestions': PropertySchema(
      id: 4,
      name: r'userRequestsAiSuggestions',
      type: IsarType.bool,
    )
  },
  estimateSize: _appLocalSettingsEstimateSize,
  serialize: _appLocalSettingsSerialize,
  deserialize: _appLocalSettingsDeserialize,
  deserializeProp: _appLocalSettingsDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {r'SalesforceEnvironment': SalesforceEnvironmentSchema},
  getId: _appLocalSettingsGetId,
  getLinks: _appLocalSettingsGetLinks,
  attach: _appLocalSettingsAttach,
  version: '3.1.0+1',
);

int _appLocalSettingsEstimateSize(
  AppLocalSettings object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 +
      SalesforceEnvironmentSchema.estimateSize(object.selectedEnvironment,
          allOffsets[SalesforceEnvironment]!, allOffsets);
  return bytesCount;
}

void _appLocalSettingsSerialize(
  AppLocalSettings object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeBool(offsets[0], object.aiSuggestionsEnabled);
  writer.writeBool(offsets[1], object.hasRequestedPushPermission);
  writer.writeObject<SalesforceEnvironment>(
    offsets[2],
    allOffsets,
    SalesforceEnvironmentSchema.serialize,
    object.selectedEnvironment,
  );
  writer.writeBool(offsets[3], object.showDevOptions);
  writer.writeBool(offsets[4], object.userRequestsAiSuggestions);
}

AppLocalSettings _appLocalSettingsDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = AppLocalSettings(
    aiSuggestionsEnabled: reader.readBool(offsets[0]),
    hasRequestedPushPermission: reader.readBool(offsets[1]),
    selectedEnvironment: reader.readObjectOrNull<SalesforceEnvironment>(
          offsets[2],
          SalesforceEnvironmentSchema.deserialize,
          allOffsets,
        ) ??
        SalesforceEnvironment(),
    showDevOptions: reader.readBool(offsets[3]),
    userRequestsAiSuggestions: reader.readBool(offsets[4]),
  );
  return object;
}

P _appLocalSettingsDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readBool(offset)) as P;
    case 1:
      return (reader.readBool(offset)) as P;
    case 2:
      return (reader.readObjectOrNull<SalesforceEnvironment>(
            offset,
            SalesforceEnvironmentSchema.deserialize,
            allOffsets,
          ) ??
          SalesforceEnvironment()) as P;
    case 3:
      return (reader.readBool(offset)) as P;
    case 4:
      return (reader.readBool(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _appLocalSettingsGetId(AppLocalSettings object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _appLocalSettingsGetLinks(AppLocalSettings object) {
  return [];
}

void _appLocalSettingsAttach(
    IsarCollection<dynamic> col, Id id, AppLocalSettings object) {}

extension AppLocalSettingsQueryWhereSort
    on QueryBuilder<AppLocalSettings, AppLocalSettings, QWhere> {
  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension AppLocalSettingsQueryWhere
    on QueryBuilder<AppLocalSettings, AppLocalSettings, QWhereClause> {
  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterWhereClause> idEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension AppLocalSettingsQueryFilter
    on QueryBuilder<AppLocalSettings, AppLocalSettings, QFilterCondition> {
  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterFilterCondition>
      aiSuggestionsEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'aiSuggestionsEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterFilterCondition>
      hasRequestedPushPermissionEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'hasRequestedPushPermission',
        value: value,
      ));
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterFilterCondition>
      showDevOptionsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'showDevOptions',
        value: value,
      ));
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterFilterCondition>
      userRequestsAiSuggestionsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userRequestsAiSuggestions',
        value: value,
      ));
    });
  }
}

extension AppLocalSettingsQueryObject
    on QueryBuilder<AppLocalSettings, AppLocalSettings, QFilterCondition> {
  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterFilterCondition>
      selectedEnvironment(FilterQuery<SalesforceEnvironment> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'selectedEnvironment');
    });
  }
}

extension AppLocalSettingsQueryLinks
    on QueryBuilder<AppLocalSettings, AppLocalSettings, QFilterCondition> {}

extension AppLocalSettingsQuerySortBy
    on QueryBuilder<AppLocalSettings, AppLocalSettings, QSortBy> {
  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy>
      sortByAiSuggestionsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'aiSuggestionsEnabled', Sort.asc);
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy>
      sortByAiSuggestionsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'aiSuggestionsEnabled', Sort.desc);
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy>
      sortByHasRequestedPushPermission() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hasRequestedPushPermission', Sort.asc);
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy>
      sortByHasRequestedPushPermissionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hasRequestedPushPermission', Sort.desc);
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy>
      sortByShowDevOptions() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showDevOptions', Sort.asc);
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy>
      sortByShowDevOptionsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showDevOptions', Sort.desc);
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy>
      sortByUserRequestsAiSuggestions() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userRequestsAiSuggestions', Sort.asc);
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy>
      sortByUserRequestsAiSuggestionsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userRequestsAiSuggestions', Sort.desc);
    });
  }
}

extension AppLocalSettingsQuerySortThenBy
    on QueryBuilder<AppLocalSettings, AppLocalSettings, QSortThenBy> {
  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy>
      thenByAiSuggestionsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'aiSuggestionsEnabled', Sort.asc);
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy>
      thenByAiSuggestionsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'aiSuggestionsEnabled', Sort.desc);
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy>
      thenByHasRequestedPushPermission() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hasRequestedPushPermission', Sort.asc);
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy>
      thenByHasRequestedPushPermissionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hasRequestedPushPermission', Sort.desc);
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy>
      thenByShowDevOptions() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showDevOptions', Sort.asc);
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy>
      thenByShowDevOptionsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showDevOptions', Sort.desc);
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy>
      thenByUserRequestsAiSuggestions() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userRequestsAiSuggestions', Sort.asc);
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QAfterSortBy>
      thenByUserRequestsAiSuggestionsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userRequestsAiSuggestions', Sort.desc);
    });
  }
}

extension AppLocalSettingsQueryWhereDistinct
    on QueryBuilder<AppLocalSettings, AppLocalSettings, QDistinct> {
  QueryBuilder<AppLocalSettings, AppLocalSettings, QDistinct>
      distinctByAiSuggestionsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'aiSuggestionsEnabled');
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QDistinct>
      distinctByHasRequestedPushPermission() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'hasRequestedPushPermission');
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QDistinct>
      distinctByShowDevOptions() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'showDevOptions');
    });
  }

  QueryBuilder<AppLocalSettings, AppLocalSettings, QDistinct>
      distinctByUserRequestsAiSuggestions() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'userRequestsAiSuggestions');
    });
  }
}

extension AppLocalSettingsQueryProperty
    on QueryBuilder<AppLocalSettings, AppLocalSettings, QQueryProperty> {
  QueryBuilder<AppLocalSettings, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<AppLocalSettings, bool, QQueryOperations>
      aiSuggestionsEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'aiSuggestionsEnabled');
    });
  }

  QueryBuilder<AppLocalSettings, bool, QQueryOperations>
      hasRequestedPushPermissionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'hasRequestedPushPermission');
    });
  }

  QueryBuilder<AppLocalSettings, SalesforceEnvironment, QQueryOperations>
      selectedEnvironmentProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'selectedEnvironment');
    });
  }

  QueryBuilder<AppLocalSettings, bool, QQueryOperations>
      showDevOptionsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'showDevOptions');
    });
  }

  QueryBuilder<AppLocalSettings, bool, QQueryOperations>
      userRequestsAiSuggestionsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'userRequestsAiSuggestions');
    });
  }
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppLocalSettingsImpl _$$AppLocalSettingsImplFromJson(
        Map<String, dynamic> json) =>
    _$AppLocalSettingsImpl(
      showDevOptions: json['showDevOptions'] as bool? ?? false,
      hasRequestedPushPermission:
          json['hasRequestedPushPermission'] as bool? ?? false,
      userRequestsAiSuggestions:
          json['userRequestsAiSuggestions'] as bool? ?? false,
      aiSuggestionsEnabled: json['aiSuggestionsEnabled'] as bool? ?? true,
      selectedEnvironment: json['selectedEnvironment'] == null
          ? const SalesforceEnvironment(
              type: SalesforceEnvironmentType.production)
          : SalesforceEnvironment.fromJson(
              json['selectedEnvironment'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$AppLocalSettingsImplToJson(
        _$AppLocalSettingsImpl instance) =>
    <String, dynamic>{
      'showDevOptions': instance.showDevOptions,
      'hasRequestedPushPermission': instance.hasRequestedPushPermission,
      'userRequestsAiSuggestions': instance.userRequestsAiSuggestions,
      'aiSuggestionsEnabled': instance.aiSuggestionsEnabled,
      'selectedEnvironment': instance.selectedEnvironment.toJson(),
    };
