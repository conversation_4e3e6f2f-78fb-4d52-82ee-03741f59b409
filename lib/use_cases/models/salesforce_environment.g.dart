// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'salesforce_environment.dart';

// **************************************************************************
// IsarEmbeddedGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

const SalesforceEnvironmentSchema = Schema(
  name: r'SalesforceEnvironment',
  id: 4527126594928597408,
  properties: {
    r'customDomainBase': PropertySchema(
      id: 0,
      name: r'customDomainBase',
      type: IsarType.string,
    ),
    r'type': PropertySchema(
      id: 1,
      name: r'type',
      type: IsarType.byte,
      enumMap: _SalesforceEnvironmenttypeEnumValueMap,
    )
  },
  estimateSize: _salesforceEnvironmentEstimateSize,
  serialize: _salesforceEnvironmentSerialize,
  deserialize: _salesforceEnvironmentDeserialize,
  deserializeProp: _salesforceEnvironmentDeserializeProp,
);

int _salesforceEnvironmentEstimateSize(
  SalesforceEnvironment object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.customDomainBase;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _salesforceEnvironmentSerialize(
  SalesforceEnvironment object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.customDomainBase);
  writer.writeByte(offsets[1], object.type.index);
}

SalesforceEnvironment _salesforceEnvironmentDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = SalesforceEnvironment(
    customDomainBase: reader.readStringOrNull(offsets[0]),
    type: _SalesforceEnvironmenttypeValueEnumMap[
            reader.readByteOrNull(offsets[1])] ??
        SalesforceEnvironmentType.production,
  );
  return object;
}

P _salesforceEnvironmentDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (_SalesforceEnvironmenttypeValueEnumMap[
              reader.readByteOrNull(offset)] ??
          SalesforceEnvironmentType.production) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _SalesforceEnvironmenttypeEnumValueMap = {
  'production': 0,
  'sandbox': 1,
  'custom': 2,
  'demo': 3,
};
const _SalesforceEnvironmenttypeValueEnumMap = {
  0: SalesforceEnvironmentType.production,
  1: SalesforceEnvironmentType.sandbox,
  2: SalesforceEnvironmentType.custom,
  3: SalesforceEnvironmentType.demo,
};

extension SalesforceEnvironmentQueryFilter on QueryBuilder<
    SalesforceEnvironment, SalesforceEnvironment, QFilterCondition> {
  QueryBuilder<SalesforceEnvironment, SalesforceEnvironment,
      QAfterFilterCondition> customDomainBaseIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'customDomainBase',
      ));
    });
  }

  QueryBuilder<SalesforceEnvironment, SalesforceEnvironment,
      QAfterFilterCondition> customDomainBaseIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'customDomainBase',
      ));
    });
  }

  QueryBuilder<SalesforceEnvironment, SalesforceEnvironment,
      QAfterFilterCondition> customDomainBaseEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'customDomainBase',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SalesforceEnvironment, SalesforceEnvironment,
      QAfterFilterCondition> customDomainBaseGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'customDomainBase',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SalesforceEnvironment, SalesforceEnvironment,
      QAfterFilterCondition> customDomainBaseLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'customDomainBase',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SalesforceEnvironment, SalesforceEnvironment,
      QAfterFilterCondition> customDomainBaseBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'customDomainBase',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SalesforceEnvironment, SalesforceEnvironment,
      QAfterFilterCondition> customDomainBaseStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'customDomainBase',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SalesforceEnvironment, SalesforceEnvironment,
      QAfterFilterCondition> customDomainBaseEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'customDomainBase',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SalesforceEnvironment, SalesforceEnvironment,
          QAfterFilterCondition>
      customDomainBaseContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'customDomainBase',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SalesforceEnvironment, SalesforceEnvironment,
          QAfterFilterCondition>
      customDomainBaseMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'customDomainBase',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<SalesforceEnvironment, SalesforceEnvironment,
      QAfterFilterCondition> customDomainBaseIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'customDomainBase',
        value: '',
      ));
    });
  }

  QueryBuilder<SalesforceEnvironment, SalesforceEnvironment,
      QAfterFilterCondition> customDomainBaseIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'customDomainBase',
        value: '',
      ));
    });
  }

  QueryBuilder<SalesforceEnvironment, SalesforceEnvironment,
      QAfterFilterCondition> typeEqualTo(SalesforceEnvironmentType value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'type',
        value: value,
      ));
    });
  }

  QueryBuilder<SalesforceEnvironment, SalesforceEnvironment,
      QAfterFilterCondition> typeGreaterThan(
    SalesforceEnvironmentType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'type',
        value: value,
      ));
    });
  }

  QueryBuilder<SalesforceEnvironment, SalesforceEnvironment,
      QAfterFilterCondition> typeLessThan(
    SalesforceEnvironmentType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'type',
        value: value,
      ));
    });
  }

  QueryBuilder<SalesforceEnvironment, SalesforceEnvironment,
      QAfterFilterCondition> typeBetween(
    SalesforceEnvironmentType lower,
    SalesforceEnvironmentType upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'type',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension SalesforceEnvironmentQueryObject on QueryBuilder<
    SalesforceEnvironment, SalesforceEnvironment, QFilterCondition> {}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SalesforceEnvironmentImpl _$$SalesforceEnvironmentImplFromJson(
        Map<String, dynamic> json) =>
    _$SalesforceEnvironmentImpl(
      type: $enumDecodeNullable(
              _$SalesforceEnvironmentTypeEnumMap, json['type']) ??
          SalesforceEnvironmentType.production,
      customDomainBase: json['customDomainBase'] as String?,
    );

Map<String, dynamic> _$$SalesforceEnvironmentImplToJson(
        _$SalesforceEnvironmentImpl instance) =>
    <String, dynamic>{
      'type': _$SalesforceEnvironmentTypeEnumMap[instance.type]!,
      'customDomainBase': instance.customDomainBase,
    };

const _$SalesforceEnvironmentTypeEnumMap = {
  SalesforceEnvironmentType.production: 'production',
  SalesforceEnvironmentType.sandbox: 'sandbox',
  SalesforceEnvironmentType.custom: 'custom',
  SalesforceEnvironmentType.demo: 'demo',
};
