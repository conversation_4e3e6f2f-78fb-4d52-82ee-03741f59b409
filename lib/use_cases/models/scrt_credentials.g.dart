// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'scrt_credentials.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetScrtCredentialsCollection on Isar {
  IsarCollection<ScrtCredentials> get scrtCredentials => this.collection();
}

const ScrtCredentialsSchema = CollectionSchema(
  name: r'ScrtCredentials',
  id: 390298374146200369,
  properties: {
    r'scrtAccessToken': PropertySchema(
      id: 0,
      name: r'scrtAccessToken',
      type: IsarType.string,
    ),
    r'scrtHost': PropertySchema(
      id: 1,
      name: r'scrtHost',
      type: IsarType.string,
    )
  },
  estimateSize: _scrtCredentialsEstimateSize,
  serialize: _scrtCredentialsSerialize,
  deserialize: _scrtCredentialsDeserialize,
  deserializeProp: _scrtCredentialsDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _scrtCredentialsGetId,
  getLinks: _scrtCredentialsGetLinks,
  attach: _scrtCredentialsAttach,
  version: '3.1.0+1',
);

int _scrtCredentialsEstimateSize(
  ScrtCredentials object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.scrtAccessToken;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.scrtHost;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _scrtCredentialsSerialize(
  ScrtCredentials object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.scrtAccessToken);
  writer.writeString(offsets[1], object.scrtHost);
}

ScrtCredentials _scrtCredentialsDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = ScrtCredentials(
    scrtAccessToken: reader.readStringOrNull(offsets[0]),
    scrtHost: reader.readStringOrNull(offsets[1]),
  );
  return object;
}

P _scrtCredentialsDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _scrtCredentialsGetId(ScrtCredentials object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _scrtCredentialsGetLinks(ScrtCredentials object) {
  return [];
}

void _scrtCredentialsAttach(
    IsarCollection<dynamic> col, Id id, ScrtCredentials object) {}

extension ScrtCredentialsQueryWhereSort
    on QueryBuilder<ScrtCredentials, ScrtCredentials, QWhere> {
  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension ScrtCredentialsQueryWhere
    on QueryBuilder<ScrtCredentials, ScrtCredentials, QWhereClause> {
  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterWhereClause> idEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterWhereClause> idLessThan(
      Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension ScrtCredentialsQueryFilter
    on QueryBuilder<ScrtCredentials, ScrtCredentials, QFilterCondition> {
  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtAccessTokenIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'scrtAccessToken',
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtAccessTokenIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'scrtAccessToken',
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtAccessTokenEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'scrtAccessToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtAccessTokenGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'scrtAccessToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtAccessTokenLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'scrtAccessToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtAccessTokenBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'scrtAccessToken',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtAccessTokenStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'scrtAccessToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtAccessTokenEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'scrtAccessToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtAccessTokenContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'scrtAccessToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtAccessTokenMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'scrtAccessToken',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtAccessTokenIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'scrtAccessToken',
        value: '',
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtAccessTokenIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'scrtAccessToken',
        value: '',
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtHostIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'scrtHost',
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtHostIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'scrtHost',
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtHostEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'scrtHost',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtHostGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'scrtHost',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtHostLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'scrtHost',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtHostBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'scrtHost',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtHostStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'scrtHost',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtHostEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'scrtHost',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtHostContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'scrtHost',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtHostMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'scrtHost',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtHostIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'scrtHost',
        value: '',
      ));
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterFilterCondition>
      scrtHostIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'scrtHost',
        value: '',
      ));
    });
  }
}

extension ScrtCredentialsQueryObject
    on QueryBuilder<ScrtCredentials, ScrtCredentials, QFilterCondition> {}

extension ScrtCredentialsQueryLinks
    on QueryBuilder<ScrtCredentials, ScrtCredentials, QFilterCondition> {}

extension ScrtCredentialsQuerySortBy
    on QueryBuilder<ScrtCredentials, ScrtCredentials, QSortBy> {
  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterSortBy>
      sortByScrtAccessToken() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'scrtAccessToken', Sort.asc);
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterSortBy>
      sortByScrtAccessTokenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'scrtAccessToken', Sort.desc);
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterSortBy>
      sortByScrtHost() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'scrtHost', Sort.asc);
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterSortBy>
      sortByScrtHostDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'scrtHost', Sort.desc);
    });
  }
}

extension ScrtCredentialsQuerySortThenBy
    on QueryBuilder<ScrtCredentials, ScrtCredentials, QSortThenBy> {
  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterSortBy>
      thenByScrtAccessToken() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'scrtAccessToken', Sort.asc);
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterSortBy>
      thenByScrtAccessTokenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'scrtAccessToken', Sort.desc);
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterSortBy>
      thenByScrtHost() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'scrtHost', Sort.asc);
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QAfterSortBy>
      thenByScrtHostDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'scrtHost', Sort.desc);
    });
  }
}

extension ScrtCredentialsQueryWhereDistinct
    on QueryBuilder<ScrtCredentials, ScrtCredentials, QDistinct> {
  QueryBuilder<ScrtCredentials, ScrtCredentials, QDistinct>
      distinctByScrtAccessToken({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'scrtAccessToken',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<ScrtCredentials, ScrtCredentials, QDistinct> distinctByScrtHost(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'scrtHost', caseSensitive: caseSensitive);
    });
  }
}

extension ScrtCredentialsQueryProperty
    on QueryBuilder<ScrtCredentials, ScrtCredentials, QQueryProperty> {
  QueryBuilder<ScrtCredentials, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<ScrtCredentials, String?, QQueryOperations>
      scrtAccessTokenProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'scrtAccessToken');
    });
  }

  QueryBuilder<ScrtCredentials, String?, QQueryOperations> scrtHostProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'scrtHost');
    });
  }
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ScrtCredentialsImpl _$$ScrtCredentialsImplFromJson(
        Map<String, dynamic> json) =>
    _$ScrtCredentialsImpl(
      scrtAccessToken: json['scrtAccessToken'] as String?,
      scrtHost: json['scrtHost'] as String?,
    );

Map<String, dynamic> _$$ScrtCredentialsImplToJson(
        _$ScrtCredentialsImpl instance) =>
    <String, dynamic>{
      'scrtAccessToken': instance.scrtAccessToken,
      'scrtHost': instance.scrtHost,
    };
