// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'previously_logged_in_user.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetPreviouslyLoggedInUserCollection on Isar {
  IsarCollection<PreviouslyLoggedInUser> get previouslyLoggedInUsers =>
      this.collection();
}

const PreviouslyLoggedInUserSchema = CollectionSchema(
  name: r'PreviouslyLoggedInUser',
  id: 1713469232180451241,
  properties: {
    r'orgId': PropertySchema(
      id: 0,
      name: r'orgId',
      type: IsarType.string,
    ),
    r'presenceId': PropertySchema(
      id: 1,
      name: r'presenceId',
      type: IsarType.string,
    ),
    r'userId': PropertySchema(
      id: 2,
      name: r'userId',
      type: IsarType.string,
    )
  },
  estimateSize: _previouslyLoggedInUserEstimateSize,
  serialize: _previouslyLoggedInUserSerialize,
  deserialize: _previouslyLoggedInUserDeserialize,
  deserializeProp: _previouslyLoggedInUserDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _previouslyLoggedInUserGetId,
  getLinks: _previouslyLoggedInUserGetLinks,
  attach: _previouslyLoggedInUserAttach,
  version: '3.1.0+1',
);

int _previouslyLoggedInUserEstimateSize(
  PreviouslyLoggedInUser object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.orgId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.presenceId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.userId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _previouslyLoggedInUserSerialize(
  PreviouslyLoggedInUser object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.orgId);
  writer.writeString(offsets[1], object.presenceId);
  writer.writeString(offsets[2], object.userId);
}

PreviouslyLoggedInUser _previouslyLoggedInUserDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = PreviouslyLoggedInUser(
    orgId: reader.readStringOrNull(offsets[0]),
    presenceId: reader.readStringOrNull(offsets[1]),
    userId: reader.readStringOrNull(offsets[2]),
  );
  return object;
}

P _previouslyLoggedInUserDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _previouslyLoggedInUserGetId(PreviouslyLoggedInUser object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _previouslyLoggedInUserGetLinks(
    PreviouslyLoggedInUser object) {
  return [];
}

void _previouslyLoggedInUserAttach(
    IsarCollection<dynamic> col, Id id, PreviouslyLoggedInUser object) {}

extension PreviouslyLoggedInUserQueryWhereSort
    on QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QWhere> {
  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QAfterWhere>
      anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension PreviouslyLoggedInUserQueryWhere on QueryBuilder<
    PreviouslyLoggedInUser, PreviouslyLoggedInUser, QWhereClause> {
  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterWhereClause> idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterWhereClause> idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterWhereClause> idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterWhereClause> idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension PreviouslyLoggedInUserQueryFilter on QueryBuilder<
    PreviouslyLoggedInUser, PreviouslyLoggedInUser, QFilterCondition> {
  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> orgIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'orgId',
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> orgIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'orgId',
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> orgIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'orgId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> orgIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'orgId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> orgIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'orgId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> orgIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'orgId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> orgIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'orgId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> orgIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'orgId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
          QAfterFilterCondition>
      orgIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'orgId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
          QAfterFilterCondition>
      orgIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'orgId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> orgIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'orgId',
        value: '',
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> orgIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'orgId',
        value: '',
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> presenceIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'presenceId',
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> presenceIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'presenceId',
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> presenceIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'presenceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> presenceIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'presenceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> presenceIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'presenceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> presenceIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'presenceId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> presenceIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'presenceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> presenceIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'presenceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
          QAfterFilterCondition>
      presenceIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'presenceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
          QAfterFilterCondition>
      presenceIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'presenceId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> presenceIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'presenceId',
        value: '',
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> presenceIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'presenceId',
        value: '',
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> userIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'userId',
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> userIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'userId',
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> userIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> userIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> userIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> userIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'userId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> userIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> userIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
          QAfterFilterCondition>
      userIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
          QAfterFilterCondition>
      userIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'userId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> userIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userId',
        value: '',
      ));
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser,
      QAfterFilterCondition> userIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'userId',
        value: '',
      ));
    });
  }
}

extension PreviouslyLoggedInUserQueryObject on QueryBuilder<
    PreviouslyLoggedInUser, PreviouslyLoggedInUser, QFilterCondition> {}

extension PreviouslyLoggedInUserQueryLinks on QueryBuilder<
    PreviouslyLoggedInUser, PreviouslyLoggedInUser, QFilterCondition> {}

extension PreviouslyLoggedInUserQuerySortBy
    on QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QSortBy> {
  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QAfterSortBy>
      sortByOrgId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'orgId', Sort.asc);
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QAfterSortBy>
      sortByOrgIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'orgId', Sort.desc);
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QAfterSortBy>
      sortByPresenceId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'presenceId', Sort.asc);
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QAfterSortBy>
      sortByPresenceIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'presenceId', Sort.desc);
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QAfterSortBy>
      sortByUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userId', Sort.asc);
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QAfterSortBy>
      sortByUserIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userId', Sort.desc);
    });
  }
}

extension PreviouslyLoggedInUserQuerySortThenBy on QueryBuilder<
    PreviouslyLoggedInUser, PreviouslyLoggedInUser, QSortThenBy> {
  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QAfterSortBy>
      thenByOrgId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'orgId', Sort.asc);
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QAfterSortBy>
      thenByOrgIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'orgId', Sort.desc);
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QAfterSortBy>
      thenByPresenceId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'presenceId', Sort.asc);
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QAfterSortBy>
      thenByPresenceIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'presenceId', Sort.desc);
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QAfterSortBy>
      thenByUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userId', Sort.asc);
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QAfterSortBy>
      thenByUserIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userId', Sort.desc);
    });
  }
}

extension PreviouslyLoggedInUserQueryWhereDistinct
    on QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QDistinct> {
  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QDistinct>
      distinctByOrgId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'orgId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QDistinct>
      distinctByPresenceId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'presenceId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, PreviouslyLoggedInUser, QDistinct>
      distinctByUserId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'userId', caseSensitive: caseSensitive);
    });
  }
}

extension PreviouslyLoggedInUserQueryProperty on QueryBuilder<
    PreviouslyLoggedInUser, PreviouslyLoggedInUser, QQueryProperty> {
  QueryBuilder<PreviouslyLoggedInUser, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, String?, QQueryOperations>
      orgIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'orgId');
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, String?, QQueryOperations>
      presenceIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'presenceId');
    });
  }

  QueryBuilder<PreviouslyLoggedInUser, String?, QQueryOperations>
      userIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'userId');
    });
  }
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PreviouslyLoggedInUserImpl _$$PreviouslyLoggedInUserImplFromJson(
        Map<String, dynamic> json) =>
    _$PreviouslyLoggedInUserImpl(
      userId: json['userId'] as String?,
      orgId: json['orgId'] as String?,
      presenceId: json['presenceId'] as String?,
    );

Map<String, dynamic> _$$PreviouslyLoggedInUserImplToJson(
        _$PreviouslyLoggedInUserImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'orgId': instance.orgId,
      'presenceId': instance.presenceId,
    };
