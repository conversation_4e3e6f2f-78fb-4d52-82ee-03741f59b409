// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'credentials.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetCredentialsCollection on Isar {
  IsarCollection<Credentials> get credentials => this.collection();
}

const CredentialsSchema = CollectionSchema(
  name: r'Credentials',
  id: -8186202567649311744,
  properties: {
    r'accessToken': PropertySchema(
      id: 0,
      name: r'accessToken',
      type: IsarType.string,
    ),
    r'authorizationToken': PropertySchema(
      id: 1,
      name: r'authorizationToken',
      type: IsarType.string,
    ),
    r'authorizationTokenExpirationTime': PropertySchema(
      id: 2,
      name: r'authorizationTokenExpirationTime',
      type: IsarType.long,
    ),
    r'devicePushToken': PropertySchema(
      id: 3,
      name: r'devicePushToken',
      type: IsarType.string,
    ),
    r'fileInstanceUrl': PropertySchema(
      id: 4,
      name: r'fileInstanceUrl',
      type: IsarType.string,
    ),
    r'instanceUrl': PropertySchema(
      id: 5,
      name: r'instanceUrl',
      type: IsarType.string,
    ),
    r'orgId': PropertySchema(
      id: 6,
      name: r'orgId',
      type: IsarType.string,
    ),
    r'refreshToken': PropertySchema(
      id: 7,
      name: r'refreshToken',
      type: IsarType.string,
    ),
    r'sessionExpirationTime': PropertySchema(
      id: 8,
      name: r'sessionExpirationTime',
      type: IsarType.long,
    ),
    r'sessionId': PropertySchema(
      id: 9,
      name: r'sessionId',
      type: IsarType.string,
    ),
    r'sessionToken': PropertySchema(
      id: 10,
      name: r'sessionToken',
      type: IsarType.string,
    ),
    r'userId': PropertySchema(
      id: 11,
      name: r'userId',
      type: IsarType.string,
    ),
    r'webSocketUrl': PropertySchema(
      id: 12,
      name: r'webSocketUrl',
      type: IsarType.string,
    )
  },
  estimateSize: _credentialsEstimateSize,
  serialize: _credentialsSerialize,
  deserialize: _credentialsDeserialize,
  deserializeProp: _credentialsDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _credentialsGetId,
  getLinks: _credentialsGetLinks,
  attach: _credentialsAttach,
  version: '3.1.0+1',
);

int _credentialsEstimateSize(
  Credentials object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.accessToken;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.authorizationToken;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.devicePushToken;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.fileInstanceUrl;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.instanceUrl;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.orgId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.refreshToken;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.sessionId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.sessionToken;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.userId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.webSocketUrl;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _credentialsSerialize(
  Credentials object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.accessToken);
  writer.writeString(offsets[1], object.authorizationToken);
  writer.writeLong(offsets[2], object.authorizationTokenExpirationTime);
  writer.writeString(offsets[3], object.devicePushToken);
  writer.writeString(offsets[4], object.fileInstanceUrl);
  writer.writeString(offsets[5], object.instanceUrl);
  writer.writeString(offsets[6], object.orgId);
  writer.writeString(offsets[7], object.refreshToken);
  writer.writeLong(offsets[8], object.sessionExpirationTime);
  writer.writeString(offsets[9], object.sessionId);
  writer.writeString(offsets[10], object.sessionToken);
  writer.writeString(offsets[11], object.userId);
  writer.writeString(offsets[12], object.webSocketUrl);
}

Credentials _credentialsDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = Credentials(
    accessToken: reader.readStringOrNull(offsets[0]),
    authorizationToken: reader.readStringOrNull(offsets[1]),
    authorizationTokenExpirationTime: reader.readLongOrNull(offsets[2]),
    devicePushToken: reader.readStringOrNull(offsets[3]),
    instanceUrl: reader.readStringOrNull(offsets[5]),
    orgId: reader.readStringOrNull(offsets[6]),
    refreshToken: reader.readStringOrNull(offsets[7]),
    sessionExpirationTime: reader.readLongOrNull(offsets[8]),
    sessionId: reader.readStringOrNull(offsets[9]),
    sessionToken: reader.readStringOrNull(offsets[10]),
    userId: reader.readStringOrNull(offsets[11]),
    webSocketUrl: reader.readStringOrNull(offsets[12]),
  );
  return object;
}

P _credentialsDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readLongOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readStringOrNull(offset)) as P;
    case 8:
      return (reader.readLongOrNull(offset)) as P;
    case 9:
      return (reader.readStringOrNull(offset)) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readStringOrNull(offset)) as P;
    case 12:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _credentialsGetId(Credentials object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _credentialsGetLinks(Credentials object) {
  return [];
}

void _credentialsAttach(
    IsarCollection<dynamic> col, Id id, Credentials object) {}

extension CredentialsQueryWhereSort
    on QueryBuilder<Credentials, Credentials, QWhere> {
  QueryBuilder<Credentials, Credentials, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension CredentialsQueryWhere
    on QueryBuilder<Credentials, Credentials, QWhereClause> {
  QueryBuilder<Credentials, Credentials, QAfterWhereClause> idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterWhereClause> idNotEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterWhereClause> idGreaterThan(Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterWhereClause> idLessThan(Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension CredentialsQueryFilter
    on QueryBuilder<Credentials, Credentials, QFilterCondition> {
  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      accessTokenIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'accessToken',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      accessTokenIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'accessToken',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      accessTokenEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'accessToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      accessTokenGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'accessToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      accessTokenLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'accessToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      accessTokenBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'accessToken',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      accessTokenStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'accessToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      accessTokenEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'accessToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      accessTokenContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'accessToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      accessTokenMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'accessToken',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      accessTokenIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'accessToken',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      accessTokenIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'accessToken',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'authorizationToken',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'authorizationToken',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'authorizationToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'authorizationToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'authorizationToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'authorizationToken',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'authorizationToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'authorizationToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'authorizationToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'authorizationToken',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'authorizationToken',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'authorizationToken',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenExpirationTimeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'authorizationTokenExpirationTime',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenExpirationTimeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'authorizationTokenExpirationTime',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenExpirationTimeEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'authorizationTokenExpirationTime',
        value: value,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenExpirationTimeGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'authorizationTokenExpirationTime',
        value: value,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenExpirationTimeLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'authorizationTokenExpirationTime',
        value: value,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      authorizationTokenExpirationTimeBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'authorizationTokenExpirationTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      devicePushTokenIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'devicePushToken',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      devicePushTokenIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'devicePushToken',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      devicePushTokenEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'devicePushToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      devicePushTokenGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'devicePushToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      devicePushTokenLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'devicePushToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      devicePushTokenBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'devicePushToken',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      devicePushTokenStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'devicePushToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      devicePushTokenEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'devicePushToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      devicePushTokenContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'devicePushToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      devicePushTokenMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'devicePushToken',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      devicePushTokenIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'devicePushToken',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      devicePushTokenIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'devicePushToken',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      fileInstanceUrlIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'fileInstanceUrl',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      fileInstanceUrlIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'fileInstanceUrl',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      fileInstanceUrlEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'fileInstanceUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      fileInstanceUrlGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'fileInstanceUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      fileInstanceUrlLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'fileInstanceUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      fileInstanceUrlBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'fileInstanceUrl',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      fileInstanceUrlStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'fileInstanceUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      fileInstanceUrlEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'fileInstanceUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      fileInstanceUrlContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'fileInstanceUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      fileInstanceUrlMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'fileInstanceUrl',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      fileInstanceUrlIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'fileInstanceUrl',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      fileInstanceUrlIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'fileInstanceUrl',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> idEqualTo(
      Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      instanceUrlIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'instanceUrl',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      instanceUrlIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'instanceUrl',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      instanceUrlEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'instanceUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      instanceUrlGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'instanceUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      instanceUrlLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'instanceUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      instanceUrlBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'instanceUrl',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      instanceUrlStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'instanceUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      instanceUrlEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'instanceUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      instanceUrlContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'instanceUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      instanceUrlMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'instanceUrl',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      instanceUrlIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'instanceUrl',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      instanceUrlIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'instanceUrl',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> orgIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'orgId',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      orgIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'orgId',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> orgIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'orgId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      orgIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'orgId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> orgIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'orgId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> orgIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'orgId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> orgIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'orgId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> orgIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'orgId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> orgIdContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'orgId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> orgIdMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'orgId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> orgIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'orgId',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      orgIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'orgId',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      refreshTokenIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'refreshToken',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      refreshTokenIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'refreshToken',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      refreshTokenEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'refreshToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      refreshTokenGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'refreshToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      refreshTokenLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'refreshToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      refreshTokenBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'refreshToken',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      refreshTokenStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'refreshToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      refreshTokenEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'refreshToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      refreshTokenContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'refreshToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      refreshTokenMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'refreshToken',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      refreshTokenIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'refreshToken',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      refreshTokenIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'refreshToken',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionExpirationTimeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'sessionExpirationTime',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionExpirationTimeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'sessionExpirationTime',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionExpirationTimeEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sessionExpirationTime',
        value: value,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionExpirationTimeGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'sessionExpirationTime',
        value: value,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionExpirationTimeLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'sessionExpirationTime',
        value: value,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionExpirationTimeBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'sessionExpirationTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'sessionId',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'sessionId',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'sessionId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'sessionId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sessionId',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'sessionId',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionTokenIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'sessionToken',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionTokenIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'sessionToken',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionTokenEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sessionToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionTokenGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'sessionToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionTokenLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'sessionToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionTokenBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'sessionToken',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionTokenStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'sessionToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionTokenEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'sessionToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionTokenContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'sessionToken',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionTokenMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'sessionToken',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionTokenIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sessionToken',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      sessionTokenIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'sessionToken',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> userIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'userId',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      userIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'userId',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> userIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      userIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> userIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> userIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'userId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      userIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> userIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> userIdContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'userId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition> userIdMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'userId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      userIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userId',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      userIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'userId',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      webSocketUrlIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'webSocketUrl',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      webSocketUrlIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'webSocketUrl',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      webSocketUrlEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'webSocketUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      webSocketUrlGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'webSocketUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      webSocketUrlLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'webSocketUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      webSocketUrlBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'webSocketUrl',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      webSocketUrlStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'webSocketUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      webSocketUrlEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'webSocketUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      webSocketUrlContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'webSocketUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      webSocketUrlMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'webSocketUrl',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      webSocketUrlIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'webSocketUrl',
        value: '',
      ));
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterFilterCondition>
      webSocketUrlIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'webSocketUrl',
        value: '',
      ));
    });
  }
}

extension CredentialsQueryObject
    on QueryBuilder<Credentials, Credentials, QFilterCondition> {}

extension CredentialsQueryLinks
    on QueryBuilder<Credentials, Credentials, QFilterCondition> {}

extension CredentialsQuerySortBy
    on QueryBuilder<Credentials, Credentials, QSortBy> {
  QueryBuilder<Credentials, Credentials, QAfterSortBy> sortByAccessToken() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'accessToken', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> sortByAccessTokenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'accessToken', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      sortByAuthorizationToken() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'authorizationToken', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      sortByAuthorizationTokenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'authorizationToken', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      sortByAuthorizationTokenExpirationTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'authorizationTokenExpirationTime', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      sortByAuthorizationTokenExpirationTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'authorizationTokenExpirationTime', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> sortByDevicePushToken() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'devicePushToken', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      sortByDevicePushTokenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'devicePushToken', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> sortByFileInstanceUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fileInstanceUrl', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      sortByFileInstanceUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fileInstanceUrl', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> sortByInstanceUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'instanceUrl', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> sortByInstanceUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'instanceUrl', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> sortByOrgId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'orgId', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> sortByOrgIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'orgId', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> sortByRefreshToken() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'refreshToken', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      sortByRefreshTokenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'refreshToken', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      sortBySessionExpirationTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionExpirationTime', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      sortBySessionExpirationTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionExpirationTime', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> sortBySessionId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionId', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> sortBySessionIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionId', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> sortBySessionToken() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionToken', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      sortBySessionTokenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionToken', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> sortByUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userId', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> sortByUserIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userId', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> sortByWebSocketUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'webSocketUrl', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      sortByWebSocketUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'webSocketUrl', Sort.desc);
    });
  }
}

extension CredentialsQuerySortThenBy
    on QueryBuilder<Credentials, Credentials, QSortThenBy> {
  QueryBuilder<Credentials, Credentials, QAfterSortBy> thenByAccessToken() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'accessToken', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> thenByAccessTokenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'accessToken', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      thenByAuthorizationToken() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'authorizationToken', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      thenByAuthorizationTokenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'authorizationToken', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      thenByAuthorizationTokenExpirationTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'authorizationTokenExpirationTime', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      thenByAuthorizationTokenExpirationTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'authorizationTokenExpirationTime', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> thenByDevicePushToken() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'devicePushToken', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      thenByDevicePushTokenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'devicePushToken', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> thenByFileInstanceUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fileInstanceUrl', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      thenByFileInstanceUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fileInstanceUrl', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> thenByInstanceUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'instanceUrl', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> thenByInstanceUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'instanceUrl', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> thenByOrgId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'orgId', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> thenByOrgIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'orgId', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> thenByRefreshToken() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'refreshToken', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      thenByRefreshTokenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'refreshToken', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      thenBySessionExpirationTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionExpirationTime', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      thenBySessionExpirationTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionExpirationTime', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> thenBySessionId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionId', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> thenBySessionIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionId', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> thenBySessionToken() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionToken', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      thenBySessionTokenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionToken', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> thenByUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userId', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> thenByUserIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userId', Sort.desc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy> thenByWebSocketUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'webSocketUrl', Sort.asc);
    });
  }

  QueryBuilder<Credentials, Credentials, QAfterSortBy>
      thenByWebSocketUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'webSocketUrl', Sort.desc);
    });
  }
}

extension CredentialsQueryWhereDistinct
    on QueryBuilder<Credentials, Credentials, QDistinct> {
  QueryBuilder<Credentials, Credentials, QDistinct> distinctByAccessToken(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'accessToken', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<Credentials, Credentials, QDistinct>
      distinctByAuthorizationToken({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'authorizationToken',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<Credentials, Credentials, QDistinct>
      distinctByAuthorizationTokenExpirationTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'authorizationTokenExpirationTime');
    });
  }

  QueryBuilder<Credentials, Credentials, QDistinct> distinctByDevicePushToken(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'devicePushToken',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<Credentials, Credentials, QDistinct> distinctByFileInstanceUrl(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'fileInstanceUrl',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<Credentials, Credentials, QDistinct> distinctByInstanceUrl(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'instanceUrl', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<Credentials, Credentials, QDistinct> distinctByOrgId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'orgId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<Credentials, Credentials, QDistinct> distinctByRefreshToken(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'refreshToken', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<Credentials, Credentials, QDistinct>
      distinctBySessionExpirationTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'sessionExpirationTime');
    });
  }

  QueryBuilder<Credentials, Credentials, QDistinct> distinctBySessionId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'sessionId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<Credentials, Credentials, QDistinct> distinctBySessionToken(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'sessionToken', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<Credentials, Credentials, QDistinct> distinctByUserId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'userId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<Credentials, Credentials, QDistinct> distinctByWebSocketUrl(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'webSocketUrl', caseSensitive: caseSensitive);
    });
  }
}

extension CredentialsQueryProperty
    on QueryBuilder<Credentials, Credentials, QQueryProperty> {
  QueryBuilder<Credentials, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<Credentials, String?, QQueryOperations> accessTokenProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'accessToken');
    });
  }

  QueryBuilder<Credentials, String?, QQueryOperations>
      authorizationTokenProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'authorizationToken');
    });
  }

  QueryBuilder<Credentials, int?, QQueryOperations>
      authorizationTokenExpirationTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'authorizationTokenExpirationTime');
    });
  }

  QueryBuilder<Credentials, String?, QQueryOperations>
      devicePushTokenProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'devicePushToken');
    });
  }

  QueryBuilder<Credentials, String?, QQueryOperations>
      fileInstanceUrlProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'fileInstanceUrl');
    });
  }

  QueryBuilder<Credentials, String?, QQueryOperations> instanceUrlProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'instanceUrl');
    });
  }

  QueryBuilder<Credentials, String?, QQueryOperations> orgIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'orgId');
    });
  }

  QueryBuilder<Credentials, String?, QQueryOperations> refreshTokenProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'refreshToken');
    });
  }

  QueryBuilder<Credentials, int?, QQueryOperations>
      sessionExpirationTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'sessionExpirationTime');
    });
  }

  QueryBuilder<Credentials, String?, QQueryOperations> sessionIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'sessionId');
    });
  }

  QueryBuilder<Credentials, String?, QQueryOperations> sessionTokenProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'sessionToken');
    });
  }

  QueryBuilder<Credentials, String?, QQueryOperations> userIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'userId');
    });
  }

  QueryBuilder<Credentials, String?, QQueryOperations> webSocketUrlProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'webSocketUrl');
    });
  }
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CredentialsImpl _$$CredentialsImplFromJson(Map<String, dynamic> json) =>
    _$CredentialsImpl(
      userId: json['userId'] as String?,
      instanceUrl: json['instanceUrl'] as String?,
      accessToken: json['accessToken'] as String?,
      orgId: json['orgId'] as String?,
      authorizationToken: json['authorizationToken'] as String?,
      webSocketUrl: json['webSocketUrl'] as String?,
      sessionToken: json['sessionToken'] as String?,
      sessionId: json['sessionId'] as String?,
      refreshToken: json['refreshToken'] as String?,
      devicePushToken: json['devicePushToken'] as String?,
      authorizationTokenExpirationTime:
          (json['authorizationTokenExpirationTime'] as num?)?.toInt(),
      sessionExpirationTime: (json['sessionExpirationTime'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$CredentialsImplToJson(_$CredentialsImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'instanceUrl': instance.instanceUrl,
      'accessToken': instance.accessToken,
      'orgId': instance.orgId,
      'authorizationToken': instance.authorizationToken,
      'webSocketUrl': instance.webSocketUrl,
      'sessionToken': instance.sessionToken,
      'sessionId': instance.sessionId,
      'refreshToken': instance.refreshToken,
      'devicePushToken': instance.devicePushToken,
      'authorizationTokenExpirationTime':
          instance.authorizationTokenExpirationTime,
      'sessionExpirationTime': instance.sessionExpirationTime,
    };
