// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'presence_status.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetPresenceStatusCollection on Isar {
  IsarCollection<PresenceStatus> get presenceStatus => this.collection();
}

const PresenceStatusSchema = CollectionSchema(
  name: r'PresenceStatus',
  id: 7735621597127538949,
  properties: {
    r'id': PropertySchema(
      id: 0,
      name: r'id',
      type: IsarType.string,
    ),
    r'label': PropertySchema(
      id: 1,
      name: r'label',
      type: IsarType.string,
    ),
    r'statusOption': PropertySchema(
      id: 2,
      name: r'statusOption',
      type: IsarType.byte,
      enumMap: _PresenceStatusstatusOptionEnumValueMap,
    )
  },
  estimateSize: _presenceStatusEstimateSize,
  serialize: _presenceStatusSerialize,
  deserialize: _presenceStatusDeserialize,
  deserializeProp: _presenceStatusDeserializeProp,
  idName: r'isarId',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _presenceStatusGetId,
  getLinks: _presenceStatusGetLinks,
  attach: _presenceStatusAttach,
  version: '3.1.0+1',
);

int _presenceStatusEstimateSize(
  PresenceStatus object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 + object.id.length * 3;
  bytesCount += 3 + object.label.length * 3;
  return bytesCount;
}

void _presenceStatusSerialize(
  PresenceStatus object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.id);
  writer.writeString(offsets[1], object.label);
  writer.writeByte(offsets[2], object.statusOption.index);
}

PresenceStatus _presenceStatusDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = PresenceStatus(
    id: reader.readString(offsets[0]),
    label: reader.readString(offsets[1]),
    statusOption: _PresenceStatusstatusOptionValueEnumMap[
            reader.readByteOrNull(offsets[2])] ??
        PresenceStatusOption.online,
  );
  return object;
}

P _presenceStatusDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readString(offset)) as P;
    case 1:
      return (reader.readString(offset)) as P;
    case 2:
      return (_PresenceStatusstatusOptionValueEnumMap[
              reader.readByteOrNull(offset)] ??
          PresenceStatusOption.online) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _PresenceStatusstatusOptionEnumValueMap = {
  'online': 0,
  'offline': 1,
  'busy': 2,
};
const _PresenceStatusstatusOptionValueEnumMap = {
  0: PresenceStatusOption.online,
  1: PresenceStatusOption.offline,
  2: PresenceStatusOption.busy,
};

Id _presenceStatusGetId(PresenceStatus object) {
  return object.isarId;
}

List<IsarLinkBase<dynamic>> _presenceStatusGetLinks(PresenceStatus object) {
  return [];
}

void _presenceStatusAttach(
    IsarCollection<dynamic> col, Id id, PresenceStatus object) {}

extension PresenceStatusQueryWhereSort
    on QueryBuilder<PresenceStatus, PresenceStatus, QWhere> {
  QueryBuilder<PresenceStatus, PresenceStatus, QAfterWhere> anyIsarId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension PresenceStatusQueryWhere
    on QueryBuilder<PresenceStatus, PresenceStatus, QWhereClause> {
  QueryBuilder<PresenceStatus, PresenceStatus, QAfterWhereClause> isarIdEqualTo(
      Id isarId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: isarId,
        upper: isarId,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterWhereClause>
      isarIdNotEqualTo(Id isarId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: isarId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: isarId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: isarId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: isarId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterWhereClause>
      isarIdGreaterThan(Id isarId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: isarId, includeLower: include),
      );
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterWhereClause>
      isarIdLessThan(Id isarId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: isarId, includeUpper: include),
      );
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterWhereClause> isarIdBetween(
    Id lowerIsarId,
    Id upperIsarId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerIsarId,
        includeLower: includeLower,
        upper: upperIsarId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension PresenceStatusQueryFilter
    on QueryBuilder<PresenceStatus, PresenceStatus, QFilterCondition> {
  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition> idEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      idGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      idLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition> idBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      idContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition> idMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      isarIdEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isarId',
        value: value,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      isarIdGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'isarId',
        value: value,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      isarIdLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'isarId',
        value: value,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      isarIdBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'isarId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      labelEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'label',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      labelGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'label',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      labelLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'label',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      labelBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'label',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      labelStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'label',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      labelEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'label',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      labelContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'label',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      labelMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'label',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      labelIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'label',
        value: '',
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      labelIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'label',
        value: '',
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      statusOptionEqualTo(PresenceStatusOption value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'statusOption',
        value: value,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      statusOptionGreaterThan(
    PresenceStatusOption value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'statusOption',
        value: value,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      statusOptionLessThan(
    PresenceStatusOption value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'statusOption',
        value: value,
      ));
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterFilterCondition>
      statusOptionBetween(
    PresenceStatusOption lower,
    PresenceStatusOption upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'statusOption',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension PresenceStatusQueryObject
    on QueryBuilder<PresenceStatus, PresenceStatus, QFilterCondition> {}

extension PresenceStatusQueryLinks
    on QueryBuilder<PresenceStatus, PresenceStatus, QFilterCondition> {}

extension PresenceStatusQuerySortBy
    on QueryBuilder<PresenceStatus, PresenceStatus, QSortBy> {
  QueryBuilder<PresenceStatus, PresenceStatus, QAfterSortBy> sortById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterSortBy> sortByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterSortBy> sortByLabel() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'label', Sort.asc);
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterSortBy> sortByLabelDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'label', Sort.desc);
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterSortBy>
      sortByStatusOption() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'statusOption', Sort.asc);
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterSortBy>
      sortByStatusOptionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'statusOption', Sort.desc);
    });
  }
}

extension PresenceStatusQuerySortThenBy
    on QueryBuilder<PresenceStatus, PresenceStatus, QSortThenBy> {
  QueryBuilder<PresenceStatus, PresenceStatus, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterSortBy> thenByIsarId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isarId', Sort.asc);
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterSortBy>
      thenByIsarIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isarId', Sort.desc);
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterSortBy> thenByLabel() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'label', Sort.asc);
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterSortBy> thenByLabelDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'label', Sort.desc);
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterSortBy>
      thenByStatusOption() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'statusOption', Sort.asc);
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QAfterSortBy>
      thenByStatusOptionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'statusOption', Sort.desc);
    });
  }
}

extension PresenceStatusQueryWhereDistinct
    on QueryBuilder<PresenceStatus, PresenceStatus, QDistinct> {
  QueryBuilder<PresenceStatus, PresenceStatus, QDistinct> distinctById(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'id', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QDistinct> distinctByLabel(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'label', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatus, QDistinct>
      distinctByStatusOption() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'statusOption');
    });
  }
}

extension PresenceStatusQueryProperty
    on QueryBuilder<PresenceStatus, PresenceStatus, QQueryProperty> {
  QueryBuilder<PresenceStatus, int, QQueryOperations> isarIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isarId');
    });
  }

  QueryBuilder<PresenceStatus, String, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<PresenceStatus, String, QQueryOperations> labelProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'label');
    });
  }

  QueryBuilder<PresenceStatus, PresenceStatusOption, QQueryOperations>
      statusOptionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'statusOption');
    });
  }
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PresenceStatusImpl _$$PresenceStatusImplFromJson(Map<String, dynamic> json) =>
    _$PresenceStatusImpl(
      id: json['id'] as String,
      label: json['label'] as String,
      statusOption:
          $enumDecode(_$PresenceStatusOptionEnumMap, json['statusOption']),
    );

Map<String, dynamic> _$$PresenceStatusImplToJson(
        _$PresenceStatusImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'label': instance.label,
      'statusOption': _$PresenceStatusOptionEnumMap[instance.statusOption]!,
    };

const _$PresenceStatusOptionEnumMap = {
  PresenceStatusOption.online: 'online',
  PresenceStatusOption.offline: 'offline',
  PresenceStatusOption.busy: 'busy',
};
