// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_channel_entry.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetMessagingChannelEntryCollection on Isar {
  IsarCollection<MessagingChannelEntry> get messagingChannelEntrys =>
      this.collection();
}

const MessagingChannelEntrySchema = CollectionSchema(
  name: r'MessagingChannelEntry',
  id: 8403303943062588360,
  properties: {
    r'channelId': PropertySchema(
      id: 0,
      name: r'channelId',
      type: IsarType.object,
      target: r'SfId',
    ),
    r'isFavorite': PropertySchema(
      id: 1,
      name: r'isFavorite',
      type: IsarType.bool,
    ),
    r'messageType': PropertySchema(
      id: 2,
      name: r'messageType',
      type: IsarType.string,
    ),
    r'messagingEndUserId': PropertySchema(
      id: 3,
      name: r'messagingEndUserId',
      type: IsarType.object,
      target: r'SfId',
    ),
    r'messagingEndUserTimestamp': PropertySchema(
      id: 4,
      name: r'messagingEndUserTimestamp',
      type: IsarType.long,
    ),
    r'name': PropertySchema(
      id: 5,
      name: r'name',
      type: IsarType.string,
    )
  },
  estimateSize: _messagingChannelEntryEstimateSize,
  serialize: _messagingChannelEntrySerialize,
  deserialize: _messagingChannelEntryDeserialize,
  deserializeProp: _messagingChannelEntryDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {r'SfId': SfIdSchema},
  getId: _messagingChannelEntryGetId,
  getLinks: _messagingChannelEntryGetLinks,
  attach: _messagingChannelEntryAttach,
  version: '3.1.0+1',
);

int _messagingChannelEntryEstimateSize(
  MessagingChannelEntry object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 +
      SfIdSchema.estimateSize(object.channelId, allOffsets[SfId]!, allOffsets);
  bytesCount += 3 + object.messageType.length * 3;
  {
    final value = object.messagingEndUserId;
    if (value != null) {
      bytesCount +=
          3 + SfIdSchema.estimateSize(value, allOffsets[SfId]!, allOffsets);
    }
  }
  bytesCount += 3 + object.name.length * 3;
  return bytesCount;
}

void _messagingChannelEntrySerialize(
  MessagingChannelEntry object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeObject<SfId>(
    offsets[0],
    allOffsets,
    SfIdSchema.serialize,
    object.channelId,
  );
  writer.writeBool(offsets[1], object.isFavorite);
  writer.writeString(offsets[2], object.messageType);
  writer.writeObject<SfId>(
    offsets[3],
    allOffsets,
    SfIdSchema.serialize,
    object.messagingEndUserId,
  );
  writer.writeLong(offsets[4], object.messagingEndUserTimestamp);
  writer.writeString(offsets[5], object.name);
}

MessagingChannelEntry _messagingChannelEntryDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = MessagingChannelEntry(
    channelId: reader.readObjectOrNull<SfId>(
          offsets[0],
          SfIdSchema.deserialize,
          allOffsets,
        ) ??
        SfId(),
    isFavorite: reader.readBool(offsets[1]),
    messageType: reader.readString(offsets[2]),
    messagingEndUserId: reader.readObjectOrNull<SfId>(
      offsets[3],
      SfIdSchema.deserialize,
      allOffsets,
    ),
    messagingEndUserTimestamp: reader.readLongOrNull(offsets[4]),
    name: reader.readString(offsets[5]),
  );
  return object;
}

P _messagingChannelEntryDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readObjectOrNull<SfId>(
            offset,
            SfIdSchema.deserialize,
            allOffsets,
          ) ??
          SfId()) as P;
    case 1:
      return (reader.readBool(offset)) as P;
    case 2:
      return (reader.readString(offset)) as P;
    case 3:
      return (reader.readObjectOrNull<SfId>(
        offset,
        SfIdSchema.deserialize,
        allOffsets,
      )) as P;
    case 4:
      return (reader.readLongOrNull(offset)) as P;
    case 5:
      return (reader.readString(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _messagingChannelEntryGetId(MessagingChannelEntry object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _messagingChannelEntryGetLinks(
    MessagingChannelEntry object) {
  return [];
}

void _messagingChannelEntryAttach(
    IsarCollection<dynamic> col, Id id, MessagingChannelEntry object) {}

extension MessagingChannelEntryQueryWhereSort
    on QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QWhere> {
  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterWhere>
      anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension MessagingChannelEntryQueryWhere on QueryBuilder<MessagingChannelEntry,
    MessagingChannelEntry, QWhereClause> {
  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension MessagingChannelEntryQueryFilter on QueryBuilder<
    MessagingChannelEntry, MessagingChannelEntry, QFilterCondition> {
  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> isFavoriteEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isFavorite',
        value: value,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> messageTypeEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'messageType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> messageTypeGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'messageType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> messageTypeLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'messageType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> messageTypeBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'messageType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> messageTypeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'messageType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> messageTypeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'messageType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
          QAfterFilterCondition>
      messageTypeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'messageType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
          QAfterFilterCondition>
      messageTypeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'messageType',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> messageTypeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'messageType',
        value: '',
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> messageTypeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'messageType',
        value: '',
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> messagingEndUserIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'messagingEndUserId',
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> messagingEndUserIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'messagingEndUserId',
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> messagingEndUserTimestampIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'messagingEndUserTimestamp',
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> messagingEndUserTimestampIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'messagingEndUserTimestamp',
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> messagingEndUserTimestampEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'messagingEndUserTimestamp',
        value: value,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> messagingEndUserTimestampGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'messagingEndUserTimestamp',
        value: value,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> messagingEndUserTimestampLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'messagingEndUserTimestamp',
        value: value,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> messagingEndUserTimestampBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'messagingEndUserTimestamp',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> nameEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> nameGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> nameLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> nameBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'name',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> nameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> nameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
          QAfterFilterCondition>
      nameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
          QAfterFilterCondition>
      nameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'name',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> nameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> nameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'name',
        value: '',
      ));
    });
  }
}

extension MessagingChannelEntryQueryObject on QueryBuilder<
    MessagingChannelEntry, MessagingChannelEntry, QFilterCondition> {
  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> channelId(FilterQuery<SfId> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'channelId');
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry,
      QAfterFilterCondition> messagingEndUserId(FilterQuery<SfId> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'messagingEndUserId');
    });
  }
}

extension MessagingChannelEntryQueryLinks on QueryBuilder<MessagingChannelEntry,
    MessagingChannelEntry, QFilterCondition> {}

extension MessagingChannelEntryQuerySortBy
    on QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QSortBy> {
  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      sortByIsFavorite() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFavorite', Sort.asc);
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      sortByIsFavoriteDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFavorite', Sort.desc);
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      sortByMessageType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageType', Sort.asc);
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      sortByMessageTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageType', Sort.desc);
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      sortByMessagingEndUserTimestamp() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messagingEndUserTimestamp', Sort.asc);
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      sortByMessagingEndUserTimestampDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messagingEndUserTimestamp', Sort.desc);
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      sortByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      sortByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }
}

extension MessagingChannelEntryQuerySortThenBy
    on QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QSortThenBy> {
  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      thenByIsFavorite() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFavorite', Sort.asc);
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      thenByIsFavoriteDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFavorite', Sort.desc);
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      thenByMessageType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageType', Sort.asc);
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      thenByMessageTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageType', Sort.desc);
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      thenByMessagingEndUserTimestamp() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messagingEndUserTimestamp', Sort.asc);
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      thenByMessagingEndUserTimestampDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messagingEndUserTimestamp', Sort.desc);
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      thenByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QAfterSortBy>
      thenByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }
}

extension MessagingChannelEntryQueryWhereDistinct
    on QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QDistinct> {
  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QDistinct>
      distinctByIsFavorite() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isFavorite');
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QDistinct>
      distinctByMessageType({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'messageType', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QDistinct>
      distinctByMessagingEndUserTimestamp() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'messagingEndUserTimestamp');
    });
  }

  QueryBuilder<MessagingChannelEntry, MessagingChannelEntry, QDistinct>
      distinctByName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'name', caseSensitive: caseSensitive);
    });
  }
}

extension MessagingChannelEntryQueryProperty on QueryBuilder<
    MessagingChannelEntry, MessagingChannelEntry, QQueryProperty> {
  QueryBuilder<MessagingChannelEntry, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<MessagingChannelEntry, SfId, QQueryOperations>
      channelIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'channelId');
    });
  }

  QueryBuilder<MessagingChannelEntry, bool, QQueryOperations>
      isFavoriteProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isFavorite');
    });
  }

  QueryBuilder<MessagingChannelEntry, String, QQueryOperations>
      messageTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'messageType');
    });
  }

  QueryBuilder<MessagingChannelEntry, SfId?, QQueryOperations>
      messagingEndUserIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'messagingEndUserId');
    });
  }

  QueryBuilder<MessagingChannelEntry, int?, QQueryOperations>
      messagingEndUserTimestampProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'messagingEndUserTimestamp');
    });
  }

  QueryBuilder<MessagingChannelEntry, String, QQueryOperations> nameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'name');
    });
  }
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MessagingChannelEntryImpl _$$MessagingChannelEntryImplFromJson(
        Map<String, dynamic> json) =>
    _$MessagingChannelEntryImpl(
      channelId: const ParseSfIdConverter().fromJson(json['id']),
      messageType: json['messageType'] as String,
      name: json['name'] as String,
      isFavorite: json['isFavorite'] as bool,
      messagingEndUserId:
          const ParseSfIdConverter().fromJson(json['messagingEndUserId']),
      messagingEndUserTimestamp:
          (json['messagingEndUserTimestamp'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$MessagingChannelEntryImplToJson(
        _$MessagingChannelEntryImpl instance) =>
    <String, dynamic>{
      'id': const ParseSfIdConverter().toJson(instance.channelId),
      'messageType': instance.messageType,
      'name': instance.name,
      'isFavorite': instance.isFavorite,
      'messagingEndUserId': _$JsonConverterToJson<Object?, SfId>(
          instance.messagingEndUserId, const ParseSfIdConverter().toJson),
      'messagingEndUserTimestamp': instance.messagingEndUserTimestamp,
    };

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);
