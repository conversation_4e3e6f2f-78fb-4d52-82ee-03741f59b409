// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_error_model.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetAppErrorCollection on Isar {
  IsarCollection<AppError> get appErrors => this.collection();
}

const AppErrorSchema = CollectionSchema(
  name: r'AppError',
  id: -5774870045698820410,
  properties: {
    r'details': PropertySchema(
      id: 0,
      name: r'details',
      type: IsarType.string,
    ),
    r'iconPath': PropertySchema(
      id: 1,
      name: r'iconPath',
      type: IsarType.string,
    ),
    r'isAppException': PropertySchema(
      id: 2,
      name: r'isAppException',
      type: IsarType.bool,
    ),
    r'isReplaceable': PropertySchema(
      id: 3,
      name: r'isReplaceable',
      type: IsarType.bool,
    ),
    r'message': PropertySchema(
      id: 4,
      name: r'message',
      type: IsarType.string,
    ),
    r'shouldForceLogout': PropertySchema(
      id: 5,
      name: r'shouldForceLogout',
      type: IsarType.bool,
    ),
    r'shouldShowSystemNotification': PropertySchema(
      id: 6,
      name: r'shouldShowSystemNotification',
      type: IsarType.bool,
    ),
    r'showErrorOverlay': PropertySchema(
      id: 7,
      name: r'showErrorOverlay',
      type: IsarType.bool,
    ),
    r'statusCode': PropertySchema(
      id: 8,
      name: r'statusCode',
      type: IsarType.long,
    )
  },
  estimateSize: _appErrorEstimateSize,
  serialize: _appErrorSerialize,
  deserialize: _appErrorDeserialize,
  deserializeProp: _appErrorDeserializeProp,
  idName: r'localDbId',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _appErrorGetId,
  getLinks: _appErrorGetLinks,
  attach: _appErrorAttach,
  version: '3.1.0+1',
);

int _appErrorEstimateSize(
  AppError object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.details;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.iconPath;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.message;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _appErrorSerialize(
  AppError object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.details);
  writer.writeString(offsets[1], object.iconPath);
  writer.writeBool(offsets[2], object.isAppException);
  writer.writeBool(offsets[3], object.isReplaceable);
  writer.writeString(offsets[4], object.message);
  writer.writeBool(offsets[5], object.shouldForceLogout);
  writer.writeBool(offsets[6], object.shouldShowSystemNotification);
  writer.writeBool(offsets[7], object.showErrorOverlay);
  writer.writeLong(offsets[8], object.statusCode);
}

AppError _appErrorDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = AppError(
    details: reader.readStringOrNull(offsets[0]),
    iconPath: reader.readStringOrNull(offsets[1]),
    isAppException: reader.readBool(offsets[2]),
    isReplaceable: reader.readBool(offsets[3]),
    message: reader.readStringOrNull(offsets[4]),
    shouldForceLogout: reader.readBool(offsets[5]),
    shouldShowSystemNotification: reader.readBool(offsets[6]),
    showErrorOverlay: reader.readBool(offsets[7]),
    statusCode: reader.readLongOrNull(offsets[8]),
  );
  return object;
}

P _appErrorDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readBool(offset)) as P;
    case 3:
      return (reader.readBool(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readBool(offset)) as P;
    case 6:
      return (reader.readBool(offset)) as P;
    case 7:
      return (reader.readBool(offset)) as P;
    case 8:
      return (reader.readLongOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _appErrorGetId(AppError object) {
  return object.localDbId;
}

List<IsarLinkBase<dynamic>> _appErrorGetLinks(AppError object) {
  return [];
}

void _appErrorAttach(IsarCollection<dynamic> col, Id id, AppError object) {}

extension AppErrorQueryWhereSort on QueryBuilder<AppError, AppError, QWhere> {
  QueryBuilder<AppError, AppError, QAfterWhere> anyLocalDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension AppErrorQueryWhere on QueryBuilder<AppError, AppError, QWhereClause> {
  QueryBuilder<AppError, AppError, QAfterWhereClause> localDbIdEqualTo(
      Id localDbId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: localDbId,
        upper: localDbId,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterWhereClause> localDbIdNotEqualTo(
      Id localDbId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: localDbId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localDbId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localDbId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: localDbId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<AppError, AppError, QAfterWhereClause> localDbIdGreaterThan(
      Id localDbId,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: localDbId, includeLower: include),
      );
    });
  }

  QueryBuilder<AppError, AppError, QAfterWhereClause> localDbIdLessThan(
      Id localDbId,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: localDbId, includeUpper: include),
      );
    });
  }

  QueryBuilder<AppError, AppError, QAfterWhereClause> localDbIdBetween(
    Id lowerLocalDbId,
    Id upperLocalDbId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerLocalDbId,
        includeLower: includeLower,
        upper: upperLocalDbId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension AppErrorQueryFilter
    on QueryBuilder<AppError, AppError, QFilterCondition> {
  QueryBuilder<AppError, AppError, QAfterFilterCondition> detailsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'details',
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> detailsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'details',
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> detailsEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'details',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> detailsGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'details',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> detailsLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'details',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> detailsBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'details',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> detailsStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'details',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> detailsEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'details',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> detailsContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'details',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> detailsMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'details',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> detailsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'details',
        value: '',
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> detailsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'details',
        value: '',
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> iconPathIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'iconPath',
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> iconPathIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'iconPath',
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> iconPathEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'iconPath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> iconPathGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'iconPath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> iconPathLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'iconPath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> iconPathBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'iconPath',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> iconPathStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'iconPath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> iconPathEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'iconPath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> iconPathContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'iconPath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> iconPathMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'iconPath',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> iconPathIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'iconPath',
        value: '',
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> iconPathIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'iconPath',
        value: '',
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> isAppExceptionEqualTo(
      bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isAppException',
        value: value,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> isReplaceableEqualTo(
      bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isReplaceable',
        value: value,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> localDbIdEqualTo(
      Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localDbId',
        value: value,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> localDbIdGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localDbId',
        value: value,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> localDbIdLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localDbId',
        value: value,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> localDbIdBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localDbId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> messageIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'message',
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> messageIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'message',
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> messageEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> messageGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> messageLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> messageBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'message',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> messageStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> messageEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> messageContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> messageMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'message',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> messageIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'message',
        value: '',
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> messageIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'message',
        value: '',
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition>
      shouldForceLogoutEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'shouldForceLogout',
        value: value,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition>
      shouldShowSystemNotificationEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'shouldShowSystemNotification',
        value: value,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition>
      showErrorOverlayEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'showErrorOverlay',
        value: value,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> statusCodeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'statusCode',
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition>
      statusCodeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'statusCode',
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> statusCodeEqualTo(
      int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'statusCode',
        value: value,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> statusCodeGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'statusCode',
        value: value,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> statusCodeLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'statusCode',
        value: value,
      ));
    });
  }

  QueryBuilder<AppError, AppError, QAfterFilterCondition> statusCodeBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'statusCode',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension AppErrorQueryObject
    on QueryBuilder<AppError, AppError, QFilterCondition> {}

extension AppErrorQueryLinks
    on QueryBuilder<AppError, AppError, QFilterCondition> {}

extension AppErrorQuerySortBy on QueryBuilder<AppError, AppError, QSortBy> {
  QueryBuilder<AppError, AppError, QAfterSortBy> sortByDetails() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'details', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> sortByDetailsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'details', Sort.desc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> sortByIconPath() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iconPath', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> sortByIconPathDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iconPath', Sort.desc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> sortByIsAppException() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isAppException', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> sortByIsAppExceptionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isAppException', Sort.desc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> sortByIsReplaceable() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isReplaceable', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> sortByIsReplaceableDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isReplaceable', Sort.desc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> sortByMessage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'message', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> sortByMessageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'message', Sort.desc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> sortByShouldForceLogout() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'shouldForceLogout', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> sortByShouldForceLogoutDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'shouldForceLogout', Sort.desc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy>
      sortByShouldShowSystemNotification() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'shouldShowSystemNotification', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy>
      sortByShouldShowSystemNotificationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'shouldShowSystemNotification', Sort.desc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> sortByShowErrorOverlay() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showErrorOverlay', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> sortByShowErrorOverlayDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showErrorOverlay', Sort.desc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> sortByStatusCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'statusCode', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> sortByStatusCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'statusCode', Sort.desc);
    });
  }
}

extension AppErrorQuerySortThenBy
    on QueryBuilder<AppError, AppError, QSortThenBy> {
  QueryBuilder<AppError, AppError, QAfterSortBy> thenByDetails() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'details', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> thenByDetailsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'details', Sort.desc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> thenByIconPath() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iconPath', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> thenByIconPathDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iconPath', Sort.desc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> thenByIsAppException() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isAppException', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> thenByIsAppExceptionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isAppException', Sort.desc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> thenByIsReplaceable() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isReplaceable', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> thenByIsReplaceableDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isReplaceable', Sort.desc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> thenByLocalDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localDbId', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> thenByLocalDbIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localDbId', Sort.desc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> thenByMessage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'message', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> thenByMessageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'message', Sort.desc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> thenByShouldForceLogout() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'shouldForceLogout', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> thenByShouldForceLogoutDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'shouldForceLogout', Sort.desc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy>
      thenByShouldShowSystemNotification() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'shouldShowSystemNotification', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy>
      thenByShouldShowSystemNotificationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'shouldShowSystemNotification', Sort.desc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> thenByShowErrorOverlay() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showErrorOverlay', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> thenByShowErrorOverlayDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showErrorOverlay', Sort.desc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> thenByStatusCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'statusCode', Sort.asc);
    });
  }

  QueryBuilder<AppError, AppError, QAfterSortBy> thenByStatusCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'statusCode', Sort.desc);
    });
  }
}

extension AppErrorQueryWhereDistinct
    on QueryBuilder<AppError, AppError, QDistinct> {
  QueryBuilder<AppError, AppError, QDistinct> distinctByDetails(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'details', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AppError, AppError, QDistinct> distinctByIconPath(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'iconPath', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AppError, AppError, QDistinct> distinctByIsAppException() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isAppException');
    });
  }

  QueryBuilder<AppError, AppError, QDistinct> distinctByIsReplaceable() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isReplaceable');
    });
  }

  QueryBuilder<AppError, AppError, QDistinct> distinctByMessage(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'message', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AppError, AppError, QDistinct> distinctByShouldForceLogout() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'shouldForceLogout');
    });
  }

  QueryBuilder<AppError, AppError, QDistinct>
      distinctByShouldShowSystemNotification() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'shouldShowSystemNotification');
    });
  }

  QueryBuilder<AppError, AppError, QDistinct> distinctByShowErrorOverlay() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'showErrorOverlay');
    });
  }

  QueryBuilder<AppError, AppError, QDistinct> distinctByStatusCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'statusCode');
    });
  }
}

extension AppErrorQueryProperty
    on QueryBuilder<AppError, AppError, QQueryProperty> {
  QueryBuilder<AppError, int, QQueryOperations> localDbIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localDbId');
    });
  }

  QueryBuilder<AppError, String?, QQueryOperations> detailsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'details');
    });
  }

  QueryBuilder<AppError, String?, QQueryOperations> iconPathProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'iconPath');
    });
  }

  QueryBuilder<AppError, bool, QQueryOperations> isAppExceptionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isAppException');
    });
  }

  QueryBuilder<AppError, bool, QQueryOperations> isReplaceableProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isReplaceable');
    });
  }

  QueryBuilder<AppError, String?, QQueryOperations> messageProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'message');
    });
  }

  QueryBuilder<AppError, bool, QQueryOperations> shouldForceLogoutProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'shouldForceLogout');
    });
  }

  QueryBuilder<AppError, bool, QQueryOperations>
      shouldShowSystemNotificationProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'shouldShowSystemNotification');
    });
  }

  QueryBuilder<AppError, bool, QQueryOperations> showErrorOverlayProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'showErrorOverlay');
    });
  }

  QueryBuilder<AppError, int?, QQueryOperations> statusCodeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'statusCode');
    });
  }
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppErrorImpl _$$AppErrorImplFromJson(Map<String, dynamic> json) =>
    _$AppErrorImpl(
      message: json['message'] as String?,
      details: json['details'] as String?,
      iconPath: json['iconPath'] as String?,
      statusCode: (json['statusCode'] as num?)?.toInt(),
      isAppException: json['isAppException'] as bool? ?? false,
      showErrorOverlay: json['showErrorOverlay'] as bool? ?? false,
      shouldForceLogout: json['shouldForceLogout'] as bool? ?? false,
      shouldShowSystemNotification:
          json['shouldShowSystemNotification'] as bool? ?? false,
      isReplaceable: json['isReplaceable'] as bool? ?? false,
    );

Map<String, dynamic> _$$AppErrorImplToJson(_$AppErrorImpl instance) =>
    <String, dynamic>{
      'message': instance.message,
      'details': instance.details,
      'iconPath': instance.iconPath,
      'statusCode': instance.statusCode,
      'isAppException': instance.isAppException,
      'showErrorOverlay': instance.showErrorOverlay,
      'shouldForceLogout': instance.shouldForceLogout,
      'shouldShowSystemNotification': instance.shouldShowSystemNotification,
      'isReplaceable': instance.isReplaceable,
    };
