import 'dart:io';

import 'package:country_picker/country_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_it/get_it.dart';
import 'package:x1440/frameworks/remote_config/app_config.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/ui/themes/themeConstants.dart';
import 'package:x1440/ui/themes/themes.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart'
    as secure_storage;
import 'package:x1440/use_cases/settings/settings_use_case.dart';
import 'toast_options.dart';

enum ToastType { error, warning, info }

class Utils {
  static const env = String.fromEnvironment('env', defaultValue: 'dev');
  static const brand = String.fromEnvironment('brand', defaultValue: '1440');
  static const isIntegrationTest =
      bool.fromEnvironment('integration_test', defaultValue: false);

  static Country getCountryDialingCode() {
    String countryCode = Platform.localeName.split('_')[1];
    try {
      return CountryParser.parse(countryCode);
    } catch (e) {
      countryCode = 'US';
      return CountryParser.parse(countryCode);
    }
  }

  static String getLanguage() {
    return Platform.localeName.split('_')[0];
  }

  static String getCountryCode() {
    return Platform.localeName.split('_')[1];
  }

  static String getLocale() {
    return Platform.localeName;
  }

  static bool checkIfMessagingSessionId(SfId? sessionId) {
    return sessionId?.toString().startsWith('0Mw') == true;
  }

  /// *************************************************************
  /// Toasts
  /// *************************************************************
  static void showToast(String message, {ToastType type = ToastType.info}) {
    // TODO: handle colors from theme light/dark (maybe?)
    ToastOptions options;
    switch (type) {
      case ToastType.error:
        options = ToastOptions(
            backgroundColor: ThemeConstants.colors.red,
            textColor: ThemeConstants.colors.white);
        break;
      case ToastType.warning:
        options = ToastOptions(
            backgroundColor: ThemeConstants.colors.orange,
            textColor: ThemeConstants.colors.appBlack);
        break;
      case ToastType.info:
        options = ToastOptions(
            backgroundColor: ThemeConstants.colors.midLightGrey,
            textColor: ThemeConstants.colors.appBlack);
        break;
    }
    try {
      _showToast(message, options);
    } catch (e) {
      GetIt.I<LoggingUseCase>()
          .getRemoteLogger('toast')
          .error('Error showing toast: $e');
    }
  }

  static void _showToast(String message, [ToastOptions? options]) {
    try {
      Fluttertoast.showToast(
          msg: message,
          gravity: options?.gravity ?? ToastGravity.BOTTOM,
          timeInSecForIosWeb: 3,
          backgroundColor:
          options?.backgroundColor, // ?? ThemeConstants.colors.midLightGrey,
          textColor: options?.textColor, // ?? ThemeConstants.colors.appBlack,
          fontSize: options?.fontSize ??
              getTextTheme(ThemeData()).headlineMedium?.fontSize);
    } catch (e, stack) {
      GetIt.I<LoggingUseCase>()
          .getRemoteLogger('FlutterToast')
          .error('Error showing toast: $e; stacktrace: \n$stack');
    }
  }

  static void closeAllToasts() {
    Fluttertoast.cancel();
  }

  static void showAlertToast(Widget child, BuildContext context) {
    FToast fToast = FToast();
    fToast.init(context);
    fToast.showToast(
      child: child,
      gravity: ToastGravity.TOP,
      toastDuration: Duration(seconds: 3),
      // isDismissable: true,
    );
  }

  static Future<void> saveDataToSecureStorage(String key, String? data) async {
    if (data != null) {
      final storage = secure_storage.FlutterSecureStorage(
          aOptions: secure_storage.AndroidOptions(
            sharedPreferencesName: "1440",
            preferencesKeyPrefix:
                "1440", // If not set a random string is generated. On native, key will be: 1440_<property name>>
            encryptedSharedPreferences: false,
            /// IMPORTANT!!! when upgrading FlutterSecureStorage, this needs to be set back to 'true'
            // encryptedSharedPreferences: true,
          ),
          iOptions: secure_storage.IOSOptions(groupId: "group.io.1440"));

      await storage.write(key: key, value: data);

      if (kDebugMode) {
        final value = await storage.read(key: key);
        print("Saved value: $value");
      }
    }
  }

  static Future<void> clearDataFromSecureStorage() async {
    final storage = secure_storage.FlutterSecureStorage(
        aOptions: secure_storage.AndroidOptions(
          sharedPreferencesName: "1440",
          preferencesKeyPrefix:
              "1440", // If not set a random string is generated. On native, key will be: 1440_<property name>>
          /// IMPORTANT!!! when upgrading FlutterSecureStorage, this needs to be set back to 'true'
          encryptedSharedPreferences: false,
        ),
        iOptions: secure_storage.IOSOptions(groupId: "group.io.1440"));

    await storage.deleteAll();
  }

  static Future<SalesforceConfig> getSfConfigFromSelectedEnvironment() async {
    final appLocalSettings =
        await GetIt.instance<SettingsUseCase>().appLocalSettings;
    final appConfig = GetIt.instance<AppConfig>();
    final env = appLocalSettings.selectedEnvironment;

    switch (env.type) {
      case SalesforceEnvironmentType.production:
        return appConfig.productionSalesforceConfig!;
      case SalesforceEnvironmentType.sandbox:
        return appConfig.sandboxSalesforceConfig!;
      case SalesforceEnvironmentType.custom:
        return appConfig.productionSalesforceConfig!.copyWith(
            endPointBase: appLocalSettings.selectedEnvironment.domain);
      default:
        return appConfig.sandboxSalesforceConfig!;
    }
  }
}
