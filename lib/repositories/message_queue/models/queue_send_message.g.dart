// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'queue_send_message.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetQueueSendMessageCollection on Isar {
  IsarCollection<QueueSendMessage> get queueSendMessages => this.collection();
}

const QueueSendMessageSchema = CollectionSchema(
  name: r'QueueSendMessage',
  id: 6657506004412322530,
  properties: {
    r'definitionId': PropertySchema(
      id: 0,
      name: r'definitionId',
      type: IsarType.object,
      target: r'SfId',
    ),
    r'definitionName': PropertySchema(
      id: 1,
      name: r'definitionName',
      type: IsarType.string,
    ),
    r'filePathsToAttach': PropertySchema(
      id: 2,
      name: r'filePathsToAttach',
      type: IsarType.stringList,
    ),
    r'messageContent': PropertySchema(
      id: 3,
      name: r'messageContent',
      type: IsarType.string,
    ),
    r'messageId': PropertySchema(
      id: 4,
      name: r'messageId',
      type: IsarType.string,
    ),
    r'messagingDefinitionId': PropertySchema(
      id: 5,
      name: r'messagingDefinitionId',
      type: IsarType.object,
      target: r'SfId',
    ),
    r'messagingEndUserId': PropertySchema(
      id: 6,
      name: r'messagingEndUserId',
      type: IsarType.object,
      target: r'SfId',
    ),
    r'type': PropertySchema(
      id: 7,
      name: r'type',
      type: IsarType.byte,
      enumMap: _QueueSendMessagetypeEnumValueMap,
    ),
    r'workTargetId': PropertySchema(
      id: 8,
      name: r'workTargetId',
      type: IsarType.object,
      target: r'SfId',
    )
  },
  estimateSize: _queueSendMessageEstimateSize,
  serialize: _queueSendMessageSerialize,
  deserialize: _queueSendMessageDeserialize,
  deserializeProp: _queueSendMessageDeserializeProp,
  idName: r'localDbId',
  indexes: {},
  links: {},
  embeddedSchemas: {r'SfId': SfIdSchema},
  getId: _queueSendMessageGetId,
  getLinks: _queueSendMessageGetLinks,
  attach: _queueSendMessageAttach,
  version: '3.1.0+1',
);

int _queueSendMessageEstimateSize(
  QueueSendMessage object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.definitionId;
    if (value != null) {
      bytesCount +=
          3 + SfIdSchema.estimateSize(value, allOffsets[SfId]!, allOffsets);
    }
  }
  {
    final value = object.definitionName;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.filePathsToAttach.length * 3;
  {
    for (var i = 0; i < object.filePathsToAttach.length; i++) {
      final value = object.filePathsToAttach[i];
      bytesCount += value.length * 3;
    }
  }
  {
    final value = object.messageContent;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.messageId.length * 3;
  {
    final value = object.messagingDefinitionId;
    if (value != null) {
      bytesCount +=
          3 + SfIdSchema.estimateSize(value, allOffsets[SfId]!, allOffsets);
    }
  }
  {
    final value = object.messagingEndUserId;
    if (value != null) {
      bytesCount +=
          3 + SfIdSchema.estimateSize(value, allOffsets[SfId]!, allOffsets);
    }
  }
  {
    final value = object.workTargetId;
    if (value != null) {
      bytesCount +=
          3 + SfIdSchema.estimateSize(value, allOffsets[SfId]!, allOffsets);
    }
  }
  return bytesCount;
}

void _queueSendMessageSerialize(
  QueueSendMessage object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeObject<SfId>(
    offsets[0],
    allOffsets,
    SfIdSchema.serialize,
    object.definitionId,
  );
  writer.writeString(offsets[1], object.definitionName);
  writer.writeStringList(offsets[2], object.filePathsToAttach);
  writer.writeString(offsets[3], object.messageContent);
  writer.writeString(offsets[4], object.messageId);
  writer.writeObject<SfId>(
    offsets[5],
    allOffsets,
    SfIdSchema.serialize,
    object.messagingDefinitionId,
  );
  writer.writeObject<SfId>(
    offsets[6],
    allOffsets,
    SfIdSchema.serialize,
    object.messagingEndUserId,
  );
  writer.writeByte(offsets[7], object.type.index);
  writer.writeObject<SfId>(
    offsets[8],
    allOffsets,
    SfIdSchema.serialize,
    object.workTargetId,
  );
}

QueueSendMessage _queueSendMessageDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = QueueSendMessage(
    definitionId: reader.readObjectOrNull<SfId>(
      offsets[0],
      SfIdSchema.deserialize,
      allOffsets,
    ),
    definitionName: reader.readStringOrNull(offsets[1]),
    filePathsToAttach: reader.readStringList(offsets[2]) ?? [],
    messageContent: reader.readStringOrNull(offsets[3]),
    messageId: reader.readString(offsets[4]),
    messagingDefinitionId: reader.readObjectOrNull<SfId>(
      offsets[5],
      SfIdSchema.deserialize,
      allOffsets,
    ),
    messagingEndUserId: reader.readObjectOrNull<SfId>(
      offsets[6],
      SfIdSchema.deserialize,
      allOffsets,
    ),
    type:
        _QueueSendMessagetypeValueEnumMap[reader.readByteOrNull(offsets[7])] ??
            QueueSendMessageType.workMessage,
    workTargetId: reader.readObjectOrNull<SfId>(
      offsets[8],
      SfIdSchema.deserialize,
      allOffsets,
    ),
  );
  return object;
}

P _queueSendMessageDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readObjectOrNull<SfId>(
        offset,
        SfIdSchema.deserialize,
        allOffsets,
      )) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readStringList(offset) ?? []) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readString(offset)) as P;
    case 5:
      return (reader.readObjectOrNull<SfId>(
        offset,
        SfIdSchema.deserialize,
        allOffsets,
      )) as P;
    case 6:
      return (reader.readObjectOrNull<SfId>(
        offset,
        SfIdSchema.deserialize,
        allOffsets,
      )) as P;
    case 7:
      return (_QueueSendMessagetypeValueEnumMap[
              reader.readByteOrNull(offset)] ??
          QueueSendMessageType.workMessage) as P;
    case 8:
      return (reader.readObjectOrNull<SfId>(
        offset,
        SfIdSchema.deserialize,
        allOffsets,
      )) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _QueueSendMessagetypeEnumValueMap = {
  'workMessage': 0,
  'outboundMessage': 1,
};
const _QueueSendMessagetypeValueEnumMap = {
  0: QueueSendMessageType.workMessage,
  1: QueueSendMessageType.outboundMessage,
};

Id _queueSendMessageGetId(QueueSendMessage object) {
  return object.localDbId;
}

List<IsarLinkBase<dynamic>> _queueSendMessageGetLinks(QueueSendMessage object) {
  return [];
}

void _queueSendMessageAttach(
    IsarCollection<dynamic> col, Id id, QueueSendMessage object) {}

extension QueueSendMessageQueryWhereSort
    on QueryBuilder<QueueSendMessage, QueueSendMessage, QWhere> {
  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterWhere> anyLocalDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension QueueSendMessageQueryWhere
    on QueryBuilder<QueueSendMessage, QueueSendMessage, QWhereClause> {
  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterWhereClause>
      localDbIdEqualTo(Id localDbId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: localDbId,
        upper: localDbId,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterWhereClause>
      localDbIdNotEqualTo(Id localDbId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: localDbId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localDbId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localDbId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: localDbId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterWhereClause>
      localDbIdGreaterThan(Id localDbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: localDbId, includeLower: include),
      );
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterWhereClause>
      localDbIdLessThan(Id localDbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: localDbId, includeUpper: include),
      );
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterWhereClause>
      localDbIdBetween(
    Id lowerLocalDbId,
    Id upperLocalDbId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerLocalDbId,
        includeLower: includeLower,
        upper: upperLocalDbId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension QueueSendMessageQueryFilter
    on QueryBuilder<QueueSendMessage, QueueSendMessage, QFilterCondition> {
  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      definitionIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'definitionId',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      definitionIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'definitionId',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      definitionNameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'definitionName',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      definitionNameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'definitionName',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      definitionNameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'definitionName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      definitionNameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'definitionName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      definitionNameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'definitionName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      definitionNameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'definitionName',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      definitionNameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'definitionName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      definitionNameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'definitionName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      definitionNameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'definitionName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      definitionNameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'definitionName',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      definitionNameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'definitionName',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      definitionNameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'definitionName',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      filePathsToAttachElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filePathsToAttach',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      filePathsToAttachElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'filePathsToAttach',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      filePathsToAttachElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'filePathsToAttach',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      filePathsToAttachElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'filePathsToAttach',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      filePathsToAttachElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'filePathsToAttach',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      filePathsToAttachElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'filePathsToAttach',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      filePathsToAttachElementContains(String value,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'filePathsToAttach',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      filePathsToAttachElementMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'filePathsToAttach',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      filePathsToAttachElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filePathsToAttach',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      filePathsToAttachElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'filePathsToAttach',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      filePathsToAttachLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'filePathsToAttach',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      filePathsToAttachIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'filePathsToAttach',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      filePathsToAttachIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'filePathsToAttach',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      filePathsToAttachLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'filePathsToAttach',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      filePathsToAttachLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'filePathsToAttach',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      filePathsToAttachLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'filePathsToAttach',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      localDbIdEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localDbId',
        value: value,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      localDbIdGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localDbId',
        value: value,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      localDbIdLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localDbId',
        value: value,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      localDbIdBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localDbId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageContentIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'messageContent',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageContentIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'messageContent',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageContentEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'messageContent',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageContentGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'messageContent',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageContentLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'messageContent',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageContentBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'messageContent',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageContentStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'messageContent',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageContentEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'messageContent',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageContentContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'messageContent',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageContentMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'messageContent',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageContentIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'messageContent',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageContentIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'messageContent',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageIdEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'messageId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageIdGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'messageId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageIdLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'messageId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageIdBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'messageId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'messageId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'messageId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'messageId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'messageId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'messageId',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messageIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'messageId',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messagingDefinitionIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'messagingDefinitionId',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messagingDefinitionIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'messagingDefinitionId',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messagingEndUserIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'messagingEndUserId',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messagingEndUserIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'messagingEndUserId',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      typeEqualTo(QueueSendMessageType value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'type',
        value: value,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      typeGreaterThan(
    QueueSendMessageType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'type',
        value: value,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      typeLessThan(
    QueueSendMessageType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'type',
        value: value,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      typeBetween(
    QueueSendMessageType lower,
    QueueSendMessageType upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'type',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      workTargetIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'workTargetId',
      ));
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      workTargetIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'workTargetId',
      ));
    });
  }
}

extension QueueSendMessageQueryObject
    on QueryBuilder<QueueSendMessage, QueueSendMessage, QFilterCondition> {
  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      definitionId(FilterQuery<SfId> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'definitionId');
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messagingDefinitionId(FilterQuery<SfId> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'messagingDefinitionId');
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      messagingEndUserId(FilterQuery<SfId> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'messagingEndUserId');
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterFilterCondition>
      workTargetId(FilterQuery<SfId> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'workTargetId');
    });
  }
}

extension QueueSendMessageQueryLinks
    on QueryBuilder<QueueSendMessage, QueueSendMessage, QFilterCondition> {}

extension QueueSendMessageQuerySortBy
    on QueryBuilder<QueueSendMessage, QueueSendMessage, QSortBy> {
  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy>
      sortByDefinitionName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'definitionName', Sort.asc);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy>
      sortByDefinitionNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'definitionName', Sort.desc);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy>
      sortByMessageContent() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageContent', Sort.asc);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy>
      sortByMessageContentDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageContent', Sort.desc);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy>
      sortByMessageId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageId', Sort.asc);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy>
      sortByMessageIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageId', Sort.desc);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy> sortByType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'type', Sort.asc);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy>
      sortByTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'type', Sort.desc);
    });
  }
}

extension QueueSendMessageQuerySortThenBy
    on QueryBuilder<QueueSendMessage, QueueSendMessage, QSortThenBy> {
  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy>
      thenByDefinitionName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'definitionName', Sort.asc);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy>
      thenByDefinitionNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'definitionName', Sort.desc);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy>
      thenByLocalDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localDbId', Sort.asc);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy>
      thenByLocalDbIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localDbId', Sort.desc);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy>
      thenByMessageContent() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageContent', Sort.asc);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy>
      thenByMessageContentDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageContent', Sort.desc);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy>
      thenByMessageId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageId', Sort.asc);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy>
      thenByMessageIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageId', Sort.desc);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy> thenByType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'type', Sort.asc);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QAfterSortBy>
      thenByTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'type', Sort.desc);
    });
  }
}

extension QueueSendMessageQueryWhereDistinct
    on QueryBuilder<QueueSendMessage, QueueSendMessage, QDistinct> {
  QueryBuilder<QueueSendMessage, QueueSendMessage, QDistinct>
      distinctByDefinitionName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'definitionName',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QDistinct>
      distinctByFilePathsToAttach() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'filePathsToAttach');
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QDistinct>
      distinctByMessageContent({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'messageContent',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QDistinct>
      distinctByMessageId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'messageId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessage, QDistinct> distinctByType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'type');
    });
  }
}

extension QueueSendMessageQueryProperty
    on QueryBuilder<QueueSendMessage, QueueSendMessage, QQueryProperty> {
  QueryBuilder<QueueSendMessage, int, QQueryOperations> localDbIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localDbId');
    });
  }

  QueryBuilder<QueueSendMessage, SfId?, QQueryOperations>
      definitionIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'definitionId');
    });
  }

  QueryBuilder<QueueSendMessage, String?, QQueryOperations>
      definitionNameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'definitionName');
    });
  }

  QueryBuilder<QueueSendMessage, List<String>, QQueryOperations>
      filePathsToAttachProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'filePathsToAttach');
    });
  }

  QueryBuilder<QueueSendMessage, String?, QQueryOperations>
      messageContentProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'messageContent');
    });
  }

  QueryBuilder<QueueSendMessage, String, QQueryOperations> messageIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'messageId');
    });
  }

  QueryBuilder<QueueSendMessage, SfId?, QQueryOperations>
      messagingDefinitionIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'messagingDefinitionId');
    });
  }

  QueryBuilder<QueueSendMessage, SfId?, QQueryOperations>
      messagingEndUserIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'messagingEndUserId');
    });
  }

  QueryBuilder<QueueSendMessage, QueueSendMessageType, QQueryOperations>
      typeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'type');
    });
  }

  QueryBuilder<QueueSendMessage, SfId?, QQueryOperations>
      workTargetIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'workTargetId');
    });
  }
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$QueueSendMessageImpl _$$QueueSendMessageImplFromJson(
        Map<String, dynamic> json) =>
    _$QueueSendMessageImpl(
      workTargetId: const ParseSfIdConverter().fromJson(json['workTargetId']),
      messageId: json['messageId'] as String,
      type: $enumDecode(_$QueueSendMessageTypeEnumMap, json['type']),
      messageContent: json['messageContent'] as String?,
      messagingDefinitionId:
          const ParseSfIdConverter().fromJson(json['messagingDefinitionId']),
      messagingEndUserId:
          const ParseSfIdConverter().fromJson(json['messagingEndUserId']),
      definitionId: const ParseSfIdConverter().fromJson(json['definitionId']),
      definitionName: json['definitionName'] as String?,
      filePathsToAttach: (json['filePathsToAttach'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const <String>[],
    );

Map<String, dynamic> _$$QueueSendMessageImplToJson(
        _$QueueSendMessageImpl instance) =>
    <String, dynamic>{
      'workTargetId': _$JsonConverterToJson<Object?, SfId>(
          instance.workTargetId, const ParseSfIdConverter().toJson),
      'messageId': instance.messageId,
      'type': _$QueueSendMessageTypeEnumMap[instance.type]!,
      'messageContent': instance.messageContent,
      'messagingDefinitionId': _$JsonConverterToJson<Object?, SfId>(
          instance.messagingDefinitionId, const ParseSfIdConverter().toJson),
      'messagingEndUserId': _$JsonConverterToJson<Object?, SfId>(
          instance.messagingEndUserId, const ParseSfIdConverter().toJson),
      'definitionId': _$JsonConverterToJson<Object?, SfId>(
          instance.definitionId, const ParseSfIdConverter().toJson),
      'definitionName': instance.definitionName,
      'filePathsToAttach': instance.filePathsToAttach,
    };

const _$QueueSendMessageTypeEnumMap = {
  QueueSendMessageType.workMessage: 'workMessage',
  QueueSendMessageType.outboundMessage: 'outboundMessage',
};

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);
