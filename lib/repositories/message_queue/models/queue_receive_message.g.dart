// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'queue_receive_message.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetQueueReceiveMessageCollection on Isar {
  IsarCollection<QueueReceiveMessage> get queueReceiveMessages =>
      this.collection();
}

const QueueReceiveMessageSchema = CollectionSchema(
  name: r'QueueReceiveMessage',
  id: 8384619276955626984,
  properties: {
    r'messageCategory': PropertySchema(
      id: 0,
      name: r'messageCategory',
      type: IsarType.string,
    ),
    r'notificationAction': PropertySchema(
      id: 1,
      name: r'notificationAction',
      type: IsarType.string,
    ),
    r'notificationAvatarName': PropertySchema(
      id: 2,
      name: r'notificationAvatarName',
      type: IsarType.string,
    ),
    r'notificationAvatarUrl': PropertySchema(
      id: 3,
      name: r'notificationAvatarUrl',
      type: IsarType.string,
    ),
    r'notificationBody': PropertySchema(
      id: 4,
      name: r'notificationBody',
      type: IsarType.string,
    ),
    r'notificationId': PropertySchema(
      id: 5,
      name: r'notificationId',
      type: IsarType.string,
    ),
    r'notificationTitle': PropertySchema(
      id: 6,
      name: r'notificationTitle',
      type: IsarType.string,
    ),
    r'sessionId': PropertySchema(
      id: 7,
      name: r'sessionId',
      type: IsarType.string,
    ),
    r'stringifiedPayload': PropertySchema(
      id: 8,
      name: r'stringifiedPayload',
      type: IsarType.string,
    ),
    r'timestamp': PropertySchema(
      id: 9,
      name: r'timestamp',
      type: IsarType.long,
    )
  },
  estimateSize: _queueReceiveMessageEstimateSize,
  serialize: _queueReceiveMessageSerialize,
  deserialize: _queueReceiveMessageDeserialize,
  deserializeProp: _queueReceiveMessageDeserializeProp,
  idName: r'localDbId',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _queueReceiveMessageGetId,
  getLinks: _queueReceiveMessageGetLinks,
  attach: _queueReceiveMessageAttach,
  version: '3.1.0+1',
);

int _queueReceiveMessageEstimateSize(
  QueueReceiveMessage object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 + object.messageCategory.length * 3;
  {
    final value = object.notificationAction;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.notificationAvatarName;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.notificationAvatarUrl;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.notificationBody;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.notificationId.length * 3;
  {
    final value = object.notificationTitle;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.sessionId.length * 3;
  bytesCount += 3 + object.stringifiedPayload.length * 3;
  return bytesCount;
}

void _queueReceiveMessageSerialize(
  QueueReceiveMessage object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.messageCategory);
  writer.writeString(offsets[1], object.notificationAction);
  writer.writeString(offsets[2], object.notificationAvatarName);
  writer.writeString(offsets[3], object.notificationAvatarUrl);
  writer.writeString(offsets[4], object.notificationBody);
  writer.writeString(offsets[5], object.notificationId);
  writer.writeString(offsets[6], object.notificationTitle);
  writer.writeString(offsets[7], object.sessionId);
  writer.writeString(offsets[8], object.stringifiedPayload);
  writer.writeLong(offsets[9], object.timestamp);
}

QueueReceiveMessage _queueReceiveMessageDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = QueueReceiveMessage(
    messageCategory: reader.readString(offsets[0]),
    notificationAction: reader.readStringOrNull(offsets[1]),
    notificationAvatarName: reader.readStringOrNull(offsets[2]),
    notificationAvatarUrl: reader.readStringOrNull(offsets[3]),
    notificationBody: reader.readStringOrNull(offsets[4]),
    notificationId: reader.readString(offsets[5]),
    notificationTitle: reader.readStringOrNull(offsets[6]),
    sessionId: reader.readString(offsets[7]),
    stringifiedPayload: reader.readString(offsets[8]),
    timestamp: reader.readLong(offsets[9]),
  );
  return object;
}

P _queueReceiveMessageDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readString(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readString(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readString(offset)) as P;
    case 8:
      return (reader.readString(offset)) as P;
    case 9:
      return (reader.readLong(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _queueReceiveMessageGetId(QueueReceiveMessage object) {
  return object.localDbId;
}

List<IsarLinkBase<dynamic>> _queueReceiveMessageGetLinks(
    QueueReceiveMessage object) {
  return [];
}

void _queueReceiveMessageAttach(
    IsarCollection<dynamic> col, Id id, QueueReceiveMessage object) {}

extension QueueReceiveMessageQueryWhereSort
    on QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QWhere> {
  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterWhere>
      anyLocalDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension QueueReceiveMessageQueryWhere
    on QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QWhereClause> {
  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterWhereClause>
      localDbIdEqualTo(Id localDbId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: localDbId,
        upper: localDbId,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterWhereClause>
      localDbIdNotEqualTo(Id localDbId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: localDbId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localDbId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localDbId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: localDbId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterWhereClause>
      localDbIdGreaterThan(Id localDbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: localDbId, includeLower: include),
      );
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterWhereClause>
      localDbIdLessThan(Id localDbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: localDbId, includeUpper: include),
      );
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterWhereClause>
      localDbIdBetween(
    Id lowerLocalDbId,
    Id upperLocalDbId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerLocalDbId,
        includeLower: includeLower,
        upper: upperLocalDbId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension QueueReceiveMessageQueryFilter on QueryBuilder<QueueReceiveMessage,
    QueueReceiveMessage, QFilterCondition> {
  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      localDbIdEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localDbId',
        value: value,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      localDbIdGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localDbId',
        value: value,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      localDbIdLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localDbId',
        value: value,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      localDbIdBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localDbId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      messageCategoryEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'messageCategory',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      messageCategoryGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'messageCategory',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      messageCategoryLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'messageCategory',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      messageCategoryBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'messageCategory',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      messageCategoryStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'messageCategory',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      messageCategoryEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'messageCategory',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      messageCategoryContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'messageCategory',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      messageCategoryMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'messageCategory',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      messageCategoryIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'messageCategory',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      messageCategoryIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'messageCategory',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationActionIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'notificationAction',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationActionIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'notificationAction',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationActionEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notificationAction',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationActionGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'notificationAction',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationActionLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'notificationAction',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationActionBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'notificationAction',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationActionStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'notificationAction',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationActionEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'notificationAction',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationActionContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'notificationAction',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationActionMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'notificationAction',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationActionIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notificationAction',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationActionIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'notificationAction',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarNameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'notificationAvatarName',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarNameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'notificationAvatarName',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarNameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notificationAvatarName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarNameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'notificationAvatarName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarNameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'notificationAvatarName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarNameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'notificationAvatarName',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarNameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'notificationAvatarName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarNameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'notificationAvatarName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarNameContains(String value,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'notificationAvatarName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarNameMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'notificationAvatarName',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarNameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notificationAvatarName',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarNameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'notificationAvatarName',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarUrlIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'notificationAvatarUrl',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarUrlIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'notificationAvatarUrl',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarUrlEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notificationAvatarUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarUrlGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'notificationAvatarUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarUrlLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'notificationAvatarUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarUrlBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'notificationAvatarUrl',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarUrlStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'notificationAvatarUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarUrlEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'notificationAvatarUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarUrlContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'notificationAvatarUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarUrlMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'notificationAvatarUrl',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarUrlIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notificationAvatarUrl',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationAvatarUrlIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'notificationAvatarUrl',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationBodyIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'notificationBody',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationBodyIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'notificationBody',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationBodyEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notificationBody',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationBodyGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'notificationBody',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationBodyLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'notificationBody',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationBodyBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'notificationBody',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationBodyStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'notificationBody',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationBodyEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'notificationBody',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationBodyContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'notificationBody',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationBodyMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'notificationBody',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationBodyIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notificationBody',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationBodyIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'notificationBody',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationIdEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notificationId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationIdGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'notificationId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationIdLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'notificationId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationIdBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'notificationId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'notificationId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'notificationId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'notificationId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'notificationId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notificationId',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'notificationId',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationTitleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'notificationTitle',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationTitleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'notificationTitle',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationTitleEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notificationTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationTitleGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'notificationTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationTitleLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'notificationTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationTitleBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'notificationTitle',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationTitleStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'notificationTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationTitleEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'notificationTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationTitleContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'notificationTitle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationTitleMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'notificationTitle',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationTitleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notificationTitle',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      notificationTitleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'notificationTitle',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      sessionIdEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      sessionIdGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      sessionIdLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      sessionIdBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'sessionId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      sessionIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      sessionIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      sessionIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'sessionId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      sessionIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'sessionId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      sessionIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sessionId',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      sessionIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'sessionId',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      stringifiedPayloadEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'stringifiedPayload',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      stringifiedPayloadGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'stringifiedPayload',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      stringifiedPayloadLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'stringifiedPayload',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      stringifiedPayloadBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'stringifiedPayload',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      stringifiedPayloadStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'stringifiedPayload',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      stringifiedPayloadEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'stringifiedPayload',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      stringifiedPayloadContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'stringifiedPayload',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      stringifiedPayloadMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'stringifiedPayload',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      stringifiedPayloadIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'stringifiedPayload',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      stringifiedPayloadIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'stringifiedPayload',
        value: '',
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      timestampEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'timestamp',
        value: value,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      timestampGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'timestamp',
        value: value,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      timestampLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'timestamp',
        value: value,
      ));
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterFilterCondition>
      timestampBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'timestamp',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension QueueReceiveMessageQueryObject on QueryBuilder<QueueReceiveMessage,
    QueueReceiveMessage, QFilterCondition> {}

extension QueueReceiveMessageQueryLinks on QueryBuilder<QueueReceiveMessage,
    QueueReceiveMessage, QFilterCondition> {}

extension QueueReceiveMessageQuerySortBy
    on QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QSortBy> {
  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByMessageCategory() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageCategory', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByMessageCategoryDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageCategory', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByNotificationAction() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationAction', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByNotificationActionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationAction', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByNotificationAvatarName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationAvatarName', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByNotificationAvatarNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationAvatarName', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByNotificationAvatarUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationAvatarUrl', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByNotificationAvatarUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationAvatarUrl', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByNotificationBody() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationBody', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByNotificationBodyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationBody', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByNotificationId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationId', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByNotificationIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationId', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByNotificationTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationTitle', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByNotificationTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationTitle', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortBySessionId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionId', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortBySessionIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionId', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByStringifiedPayload() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'stringifiedPayload', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByStringifiedPayloadDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'stringifiedPayload', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByTimestamp() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'timestamp', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      sortByTimestampDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'timestamp', Sort.desc);
    });
  }
}

extension QueueReceiveMessageQuerySortThenBy
    on QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QSortThenBy> {
  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByLocalDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localDbId', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByLocalDbIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localDbId', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByMessageCategory() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageCategory', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByMessageCategoryDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageCategory', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByNotificationAction() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationAction', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByNotificationActionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationAction', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByNotificationAvatarName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationAvatarName', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByNotificationAvatarNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationAvatarName', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByNotificationAvatarUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationAvatarUrl', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByNotificationAvatarUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationAvatarUrl', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByNotificationBody() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationBody', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByNotificationBodyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationBody', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByNotificationId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationId', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByNotificationIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationId', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByNotificationTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationTitle', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByNotificationTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationTitle', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenBySessionId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionId', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenBySessionIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sessionId', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByStringifiedPayload() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'stringifiedPayload', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByStringifiedPayloadDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'stringifiedPayload', Sort.desc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByTimestamp() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'timestamp', Sort.asc);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QAfterSortBy>
      thenByTimestampDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'timestamp', Sort.desc);
    });
  }
}

extension QueueReceiveMessageQueryWhereDistinct
    on QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QDistinct> {
  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QDistinct>
      distinctByMessageCategory({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'messageCategory',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QDistinct>
      distinctByNotificationAction({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notificationAction',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QDistinct>
      distinctByNotificationAvatarName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notificationAvatarName',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QDistinct>
      distinctByNotificationAvatarUrl({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notificationAvatarUrl',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QDistinct>
      distinctByNotificationBody({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notificationBody',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QDistinct>
      distinctByNotificationId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notificationId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QDistinct>
      distinctByNotificationTitle({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notificationTitle',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QDistinct>
      distinctBySessionId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'sessionId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QDistinct>
      distinctByStringifiedPayload({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'stringifiedPayload',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QDistinct>
      distinctByTimestamp() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'timestamp');
    });
  }
}

extension QueueReceiveMessageQueryProperty
    on QueryBuilder<QueueReceiveMessage, QueueReceiveMessage, QQueryProperty> {
  QueryBuilder<QueueReceiveMessage, int, QQueryOperations> localDbIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localDbId');
    });
  }

  QueryBuilder<QueueReceiveMessage, String, QQueryOperations>
      messageCategoryProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'messageCategory');
    });
  }

  QueryBuilder<QueueReceiveMessage, String?, QQueryOperations>
      notificationActionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notificationAction');
    });
  }

  QueryBuilder<QueueReceiveMessage, String?, QQueryOperations>
      notificationAvatarNameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notificationAvatarName');
    });
  }

  QueryBuilder<QueueReceiveMessage, String?, QQueryOperations>
      notificationAvatarUrlProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notificationAvatarUrl');
    });
  }

  QueryBuilder<QueueReceiveMessage, String?, QQueryOperations>
      notificationBodyProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notificationBody');
    });
  }

  QueryBuilder<QueueReceiveMessage, String, QQueryOperations>
      notificationIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notificationId');
    });
  }

  QueryBuilder<QueueReceiveMessage, String?, QQueryOperations>
      notificationTitleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notificationTitle');
    });
  }

  QueryBuilder<QueueReceiveMessage, String, QQueryOperations>
      sessionIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'sessionId');
    });
  }

  QueryBuilder<QueueReceiveMessage, String, QQueryOperations>
      stringifiedPayloadProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'stringifiedPayload');
    });
  }

  QueryBuilder<QueueReceiveMessage, int, QQueryOperations> timestampProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'timestamp');
    });
  }
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$QueueReceiveMessageImpl _$$QueueReceiveMessageImplFromJson(
        Map<String, dynamic> json) =>
    _$QueueReceiveMessageImpl(
      timestamp: const TimestampIntConverter().fromJson(json['timestamp']),
      notificationId: json['notificationId'] as String,
      notificationAction: json['notificationAction'] as String?,
      messageCategory: json['messageCategory'] as String,
      stringifiedPayload: json['stringifiedPayload'] as String,
      sessionId: json['sessionId'] as String,
      notificationAvatarUrl: json['notificationAvatarUrl'] as String?,
      notificationAvatarName: json['notificationAvatarName'] as String?,
      notificationTitle: json['notificationTitle'] as String?,
      notificationBody: json['notificationBody'] as String?,
    );

Map<String, dynamic> _$$QueueReceiveMessageImplToJson(
        _$QueueReceiveMessageImpl instance) =>
    <String, dynamic>{
      'timestamp': const TimestampIntConverter().toJson(instance.timestamp),
      'notificationId': instance.notificationId,
      'notificationAction': instance.notificationAction,
      'messageCategory': instance.messageCategory,
      'stringifiedPayload': instance.stringifiedPayload,
      'sessionId': instance.sessionId,
      'notificationAvatarUrl': instance.notificationAvatarUrl,
      'notificationAvatarName': instance.notificationAvatarName,
      'notificationTitle': instance.notificationTitle,
      'notificationBody': instance.notificationBody,
    };
