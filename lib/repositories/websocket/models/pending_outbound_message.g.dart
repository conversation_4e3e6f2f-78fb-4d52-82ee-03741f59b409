// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pending_outbound_message.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetPendingOutboundMessageCollection on Isar {
  IsarCollection<PendingOutboundMessage> get pendingOutboundMessages =>
      this.collection();
}

const PendingOutboundMessageSchema = CollectionSchema(
  name: r'PendingOutboundMessage',
  id: 8480959233575025779,
  properties: {
    r'contactId': PropertySchema(
      id: 0,
      name: r'contactId',
      type: IsarType.string,
    ),
    r'filePathsToAttach': PropertySchema(
      id: 1,
      name: r'filePathsToAttach',
      type: IsarType.stringList,
    ),
    r'messageContent': PropertySchema(
      id: 2,
      name: r'messageContent',
      type: IsarType.string,
    ),
    r'messageId': PropertySchema(
      id: 3,
      name: r'messageId',
      type: IsarType.string,
    )
  },
  estimateSize: _pendingOutboundMessageEstimateSize,
  serialize: _pendingOutboundMessageSerialize,
  deserialize: _pendingOutboundMessageDeserialize,
  deserializeProp: _pendingOutboundMessageDeserializeProp,
  idName: r'localDbId',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _pendingOutboundMessageGetId,
  getLinks: _pendingOutboundMessageGetLinks,
  attach: _pendingOutboundMessageAttach,
  version: '3.1.0+1',
);

int _pendingOutboundMessageEstimateSize(
  PendingOutboundMessage object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.contactId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.filePathsToAttach.length * 3;
  {
    for (var i = 0; i < object.filePathsToAttach.length; i++) {
      final value = object.filePathsToAttach[i];
      bytesCount += value.length * 3;
    }
  }
  {
    final value = object.messageContent;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.messageId.length * 3;
  return bytesCount;
}

void _pendingOutboundMessageSerialize(
  PendingOutboundMessage object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.contactId);
  writer.writeStringList(offsets[1], object.filePathsToAttach);
  writer.writeString(offsets[2], object.messageContent);
  writer.writeString(offsets[3], object.messageId);
}

PendingOutboundMessage _pendingOutboundMessageDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = PendingOutboundMessage(
    contactId: reader.readStringOrNull(offsets[0]),
    filePathsToAttach: reader.readStringList(offsets[1]) ?? [],
    messageContent: reader.readStringOrNull(offsets[2]),
    messageId: reader.readString(offsets[3]),
  );
  return object;
}

P _pendingOutboundMessageDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringList(offset) ?? []) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readString(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _pendingOutboundMessageGetId(PendingOutboundMessage object) {
  return object.localDbId;
}

List<IsarLinkBase<dynamic>> _pendingOutboundMessageGetLinks(
    PendingOutboundMessage object) {
  return [];
}

void _pendingOutboundMessageAttach(
    IsarCollection<dynamic> col, Id id, PendingOutboundMessage object) {}

extension PendingOutboundMessageQueryWhereSort
    on QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QWhere> {
  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QAfterWhere>
      anyLocalDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension PendingOutboundMessageQueryWhere on QueryBuilder<
    PendingOutboundMessage, PendingOutboundMessage, QWhereClause> {
  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterWhereClause> localDbIdEqualTo(Id localDbId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: localDbId,
        upper: localDbId,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterWhereClause> localDbIdNotEqualTo(Id localDbId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: localDbId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localDbId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localDbId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: localDbId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
          QAfterWhereClause>
      localDbIdGreaterThan(Id localDbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: localDbId, includeLower: include),
      );
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
          QAfterWhereClause>
      localDbIdLessThan(Id localDbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: localDbId, includeUpper: include),
      );
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterWhereClause> localDbIdBetween(
    Id lowerLocalDbId,
    Id upperLocalDbId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerLocalDbId,
        includeLower: includeLower,
        upper: upperLocalDbId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension PendingOutboundMessageQueryFilter on QueryBuilder<
    PendingOutboundMessage, PendingOutboundMessage, QFilterCondition> {
  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> contactIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'contactId',
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> contactIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'contactId',
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> contactIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'contactId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> contactIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'contactId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> contactIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'contactId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> contactIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'contactId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> contactIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'contactId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> contactIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'contactId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
          QAfterFilterCondition>
      contactIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'contactId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
          QAfterFilterCondition>
      contactIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'contactId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> contactIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'contactId',
        value: '',
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> contactIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'contactId',
        value: '',
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> filePathsToAttachElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filePathsToAttach',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> filePathsToAttachElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'filePathsToAttach',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> filePathsToAttachElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'filePathsToAttach',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> filePathsToAttachElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'filePathsToAttach',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> filePathsToAttachElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'filePathsToAttach',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> filePathsToAttachElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'filePathsToAttach',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
          QAfterFilterCondition>
      filePathsToAttachElementContains(String value,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'filePathsToAttach',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
          QAfterFilterCondition>
      filePathsToAttachElementMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'filePathsToAttach',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> filePathsToAttachElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filePathsToAttach',
        value: '',
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> filePathsToAttachElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'filePathsToAttach',
        value: '',
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> filePathsToAttachLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'filePathsToAttach',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> filePathsToAttachIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'filePathsToAttach',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> filePathsToAttachIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'filePathsToAttach',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> filePathsToAttachLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'filePathsToAttach',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> filePathsToAttachLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'filePathsToAttach',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> filePathsToAttachLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'filePathsToAttach',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> localDbIdEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localDbId',
        value: value,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> localDbIdGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localDbId',
        value: value,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> localDbIdLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localDbId',
        value: value,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> localDbIdBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localDbId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageContentIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'messageContent',
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageContentIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'messageContent',
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageContentEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'messageContent',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageContentGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'messageContent',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageContentLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'messageContent',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageContentBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'messageContent',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageContentStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'messageContent',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageContentEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'messageContent',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
          QAfterFilterCondition>
      messageContentContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'messageContent',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
          QAfterFilterCondition>
      messageContentMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'messageContent',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageContentIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'messageContent',
        value: '',
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageContentIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'messageContent',
        value: '',
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageIdEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'messageId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageIdGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'messageId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageIdLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'messageId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageIdBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'messageId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'messageId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'messageId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
          QAfterFilterCondition>
      messageIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'messageId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
          QAfterFilterCondition>
      messageIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'messageId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'messageId',
        value: '',
      ));
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage,
      QAfterFilterCondition> messageIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'messageId',
        value: '',
      ));
    });
  }
}

extension PendingOutboundMessageQueryObject on QueryBuilder<
    PendingOutboundMessage, PendingOutboundMessage, QFilterCondition> {}

extension PendingOutboundMessageQueryLinks on QueryBuilder<
    PendingOutboundMessage, PendingOutboundMessage, QFilterCondition> {}

extension PendingOutboundMessageQuerySortBy
    on QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QSortBy> {
  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QAfterSortBy>
      sortByContactId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'contactId', Sort.asc);
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QAfterSortBy>
      sortByContactIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'contactId', Sort.desc);
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QAfterSortBy>
      sortByMessageContent() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageContent', Sort.asc);
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QAfterSortBy>
      sortByMessageContentDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageContent', Sort.desc);
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QAfterSortBy>
      sortByMessageId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageId', Sort.asc);
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QAfterSortBy>
      sortByMessageIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageId', Sort.desc);
    });
  }
}

extension PendingOutboundMessageQuerySortThenBy on QueryBuilder<
    PendingOutboundMessage, PendingOutboundMessage, QSortThenBy> {
  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QAfterSortBy>
      thenByContactId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'contactId', Sort.asc);
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QAfterSortBy>
      thenByContactIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'contactId', Sort.desc);
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QAfterSortBy>
      thenByLocalDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localDbId', Sort.asc);
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QAfterSortBy>
      thenByLocalDbIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localDbId', Sort.desc);
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QAfterSortBy>
      thenByMessageContent() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageContent', Sort.asc);
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QAfterSortBy>
      thenByMessageContentDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageContent', Sort.desc);
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QAfterSortBy>
      thenByMessageId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageId', Sort.asc);
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QAfterSortBy>
      thenByMessageIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'messageId', Sort.desc);
    });
  }
}

extension PendingOutboundMessageQueryWhereDistinct
    on QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QDistinct> {
  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QDistinct>
      distinctByContactId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'contactId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QDistinct>
      distinctByFilePathsToAttach() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'filePathsToAttach');
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QDistinct>
      distinctByMessageContent({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'messageContent',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PendingOutboundMessage, PendingOutboundMessage, QDistinct>
      distinctByMessageId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'messageId', caseSensitive: caseSensitive);
    });
  }
}

extension PendingOutboundMessageQueryProperty on QueryBuilder<
    PendingOutboundMessage, PendingOutboundMessage, QQueryProperty> {
  QueryBuilder<PendingOutboundMessage, int, QQueryOperations>
      localDbIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localDbId');
    });
  }

  QueryBuilder<PendingOutboundMessage, String?, QQueryOperations>
      contactIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'contactId');
    });
  }

  QueryBuilder<PendingOutboundMessage, List<String>, QQueryOperations>
      filePathsToAttachProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'filePathsToAttach');
    });
  }

  QueryBuilder<PendingOutboundMessage, String?, QQueryOperations>
      messageContentProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'messageContent');
    });
  }

  QueryBuilder<PendingOutboundMessage, String, QQueryOperations>
      messageIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'messageId');
    });
  }
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PendingOutboundMessageImpl _$$PendingOutboundMessageImplFromJson(
        Map<String, dynamic> json) =>
    _$PendingOutboundMessageImpl(
      contactId: json['contactId'] as String?,
      messageId: json['messageId'] as String,
      messageContent: json['messageContent'] as String?,
      filePathsToAttach: (json['filePathsToAttach'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const <String>[],
    );

Map<String, dynamic> _$$PendingOutboundMessageImplToJson(
        _$PendingOutboundMessageImpl instance) =>
    <String, dynamic>{
      'contactId': instance.contactId,
      'messageId': instance.messageId,
      'messageContent': instance.messageContent,
      'filePathsToAttach': instance.filePathsToAttach,
    };
