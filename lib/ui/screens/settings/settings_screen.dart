import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:loggy/loggy.dart';
import 'package:package_info_plus/package_info_plus.dart';

import 'package:x1440/debug/debug_text_styles_widget.dart';
import 'package:x1440/debug/debug_viewmodel.dart';
import 'package:x1440/frameworks/push_notification_manager.dart';
import 'package:x1440/frameworks/settings/settings_manager.dart';
import 'package:x1440/frameworks/settings/settings_event.dart';
import 'package:x1440/frameworks/settings/settings_state.dart';
import 'package:x1440/generated/l10n.dart';
import 'package:x1440/models/app_error_model.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/repositories/websocket/models/websocket_connection.dart';
import 'package:x1440/ui/blocs/app_error/app_error_bloc.dart';
import 'package:x1440/ui/blocs/app_error/app_error_event.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';
import 'package:x1440/ui/blocs/auth/auth_event.dart';
import 'package:x1440/ui/blocs/auth/auth_state.dart';
import 'package:x1440/ui/blocs/messaging/messaging_bloc.dart';
import 'package:x1440/ui/blocs/messaging/messaging_state.dart';
import 'package:x1440/ui/modals/no_ai_suggestions_modal.dart';
import 'package:x1440/ui/screens/settings/settings_appbar.dart';
import 'package:x1440/ui/screens/settings/widgets/custom_domain_tile.dart';
import 'package:x1440/ui/screens/settings/widgets/settings_list_tile.dart';
import 'package:x1440/ui/themes/icons.dart';
import 'package:x1440/ui/themes/themeConstants.dart';
import 'package:x1440/utils/Utils.dart';
import 'package:x1440/utils/constants.dart';

import 'widgets/selection_list_tile.dart';

class SettingsScreen extends StatelessWidget with UiLoggy {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    SettingsManager settingsManager = GetIt.I<SettingsManager>();

    return Scaffold(
      appBar: const SettingsAppBar(),
      body: Container(
          margin: const EdgeInsets.only(top: 5),
          child: BlocBuilder<AuthBloc, AuthState>(
              builder: (context, authState) => BlocBuilder<SettingsManager,
                      SettingsState>(
                  bloc: settingsManager,
                  builder: (context, settingsState) {
                    bool isLoggedIn = authState.isLoggedIn;
                    // bool isLoggedIn = snapshot.data?.isLoggedIn ?? false;
                    return ListView(
                      children: ListTile.divideTiles(
                        context: context,
                        tiles: [
                          if (!isLoggedIn)
                            SelectionListTile(
                              title: S
                                  .of(context)
                                  .settings_screen_login_with_sandbox_title,
                              subtitle: S
                                  .of(context)
                                  .settings_screen_login_with_sandbox_subtitle,
                              icon: Padding(
                                padding: const EdgeInsets.only(top: 6.0),
                                child: SvgPicture.asset(
                                    Constants.SALESFORCE_CLOUD_ICON_SVG),
                              ),
                              onTap: () => settingsManager.add(
                                  SetEnvironmentEvent(
                                      type: SalesforceEnvironmentType.sandbox)),
                              isSelected: settingsState
                                  .settings.selectedEnvironment.isSandbox,
                            ),
                          if (!isLoggedIn) const CustomDomainTile(),
                          if (!isLoggedIn)
                            SelectionListTile(
                              title:
                                  S.of(context).settings_screen_demo_mode_title,
                              subtitle: S
                                  .of(context)
                                  .settings_screen_demo_mode_subtitle,
                              icon: Icon(AppIcon.demoMode.iconData),
                              onTap: () => settingsManager.add(
                                  SetEnvironmentEvent(
                                      type: SalesforceEnvironmentType.demo)),
                              isSelected: settingsState
                                  .settings.selectedEnvironment.isDemo,
                            ),
                          if (isLoggedIn)
                            GestureDetector(
                              onTap:
                                  settingsState.settings.aiSuggestionsEnabled ==
                                          false
                                      ? () async {
                                          await noAiSuggestionModalBottomSheet(
                                              context: context);
                                        }
                                      : null,
                              child: SettingsListTile(
                                leading: Image.asset(
                                  Constants.LOGO_OPENAI,
                                  width: 24,
                                  height: 24,
                                ),
                                title: S
                                    .of(context)
                                    .settings_screen_ai_suggestions_title,
                                subtitle: S
                                    .of(context)
                                    .settings_screen_ai_suggestions_subtitle,
                                switchValue: settingsState
                                    .settings.userRequestsAiSuggestions,
                                onToggle: (value) {
                                  settingsManager.add(
                                      ToggleAiSuggestionsEnabledEvent(value));
                                },
                                enabled:
                                    settingsState.settings.aiSuggestionsEnabled,
                              ),
                            ),
                          _DevSettingsItems(),
                        ],
                      ).toList(),
                    );
                  }))),
    );
  }
}

class _DevSettingsItems extends StatelessWidget with UiLoggy {
  Widget get websocketConnectionStatusView =>
      BlocBuilder<MessagingBloc, MessagingState>(
        bloc: GetIt.instance<MessagingBloc>(),
        builder: (context, state) => SettingsListTile(
          leading: () {
            WebsocketConnectionState connectionState =
                state.webSocketConnectionState.connectionState;
            if (connectionState.isConnected) {
              return Icon(Icons.wifi, color: ThemeConstants.colors.green);
            }
            if (connectionState == WebsocketConnectionState.connecting) {
              return Icon(Icons.wifi_2_bar_rounded,
                  color: ThemeConstants.colors.green);
            }
            if (connectionState == WebsocketConnectionState.reconnecting) {
              return Icon(Icons.wifi_1_bar_rounded,
                  color: ThemeConstants.colors.red);
            }
            return Icon(Icons.wifi_off, color: ThemeConstants.colors.red);
          }(),
          // leading: const Icon(Icons.wifi),
          title: S.of(context).settings_screen_connection_state_title,
          subtitle:
              "${S.of(context).settings_screen_connection_state_internet_subtitle}: ${state.connectivityState.name}\n${S.of(context).settings_screen_connection_state_web_socket_subtitle}: ${state.webSocketConnectionState.connectionState.name.toString()}",
          enabled: true,
        ),
      );

  @override
  Widget build(BuildContext _) => BlocBuilder<SettingsManager,
          SettingsState>(
      bloc: GetIt.I<SettingsManager>(),
      builder: (context, state) => !state.settings.showDevOptions
          ? SizedBox.shrink()
          : Column(children: [
              SettingsListTile(
                  leading: const Icon(Icons.bug_report),
                  title: S.of(context).settings_screen_show_dev_options_title,
                  subtitle:
                      S.of(context).settings_screen_show_dev_options_subtitle,
                  switchValue:
                      state.settings.showDevOptions,
                  onToggle:
                      (value) => GetIt.I<SettingsManager>().add(SetShowDevOptionsEvent(value)),
                  enabled: true),
              websocketConnectionStatusView,
              SettingsListTile(
                leading: const Icon(Icons.developer_board),
                title: "kDebugMode Value",
                subtitle: kDebugMode.toString(),
              ),
              SettingsListTile(
                  leading: const Icon(Icons.developer_mode),
                  title:
                      S.of(context).settings_screen_spoof_error_message_title,
                  subtitle: S
                      .of(context)
                      .settings_screen_spoof_error_message_subtitle,
                  onTap: () async {
                    final pushManager =
                        GetIt.instance<PushNotificationManager>();
                    String? deviceToken = await pushManager.getPushToken();

                    if (deviceToken != null) {
                      Clipboard.setData(ClipboardData(text: deviceToken));
                      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                        content: Text(S
                            .of(context)
                            .settings_screen_copy_device_token_success_label),
                        duration: const Duration(seconds: 1),
                      ));
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                        content: Text(S
                            .of(context)
                            .settings_screen_copy_device_token_failure_label),
                        duration: const Duration(seconds: 1),
                      ));
                    }
                  }),
              SettingsListTile(
                  leading: const Icon(Icons.error),
                  title:
                      S.of(context).settings_screen_spoof_error_message_title,
                  subtitle: S
                      .of(context)
                      .settings_screen_spoof_error_message_subtitle,
                  onTap: () async {
                    loggy.info(
                        '[settings_screen.dart] showing spoofed error message');
                    GetIt.I<AppErrorBloc>().add(ReportAppErrorEvent(AppError(
                        message: S
                            .of(context)
                            .settings_screen_spoof_error_message_title,
                        showErrorOverlay: true)));
                  }),
              SettingsListTile(
                  leading: const Icon(Icons.error),
                  title: S.of(context).settings_screen_crash_app_title,
                  subtitle: S.of(context).settings_screen_crash_app_subtitle,
                  onTap: () async {
                    loggy.info('Send Crashlytics Crash event');
                    FirebaseCrashlytics.instance.crash();
                  }),
              SettingsListTile(
                  leading: const Icon(Icons.error),
                  title: "Show Info Toast",
                  subtitle: '',
                  onTap: () async {
                    Utils.showToast('Test Toast Message');
                  }),
              SettingsListTile(
                  leading: const Icon(Icons.error),
                  title: "Show Warning Toast",
                  subtitle: '',
                  onTap: () async {
                    Utils.showToast('Test Warning Toast Message',
                        type: ToastType.warning);
                  }),
              SettingsListTile(
                  leading: const Icon(Icons.error),
                  title: "Show Error Toast",
                  subtitle: '',
                  onTap: () async {
                    Utils.showToast('Test Error Toast Message',
                        type: ToastType.error);
                  }),
              SettingsListTile(
                  leading: const Icon(Icons.error),
                  title: S.of(context).settings_screen_end_session_title,
                  subtitle: S.of(context).settings_screen_end_session_subtitle,
                  onTap: () async {
                    loggy.info('End session from Settings Screen');

                    GetIt.I<AuthBloc>().add(SessionExpiredEvent());
                  }),
              FutureBuilder(
                future: PackageInfo.fromPlatform(),
                builder: (BuildContext context,
                    AsyncSnapshot<PackageInfo> snapshot) {
                  loggy.info('PackageInfo snapshot: ${snapshot.data}');
                  const env = Utils.env;

                  String version =
                      '${snapshot.data?.version ?? ''}+${snapshot.data?.buildNumber ?? ''}';

                  String? buildSignature = snapshot.data?.buildSignature;
                  buildSignature =
                      (buildSignature != null && buildSignature.isNotEmpty)
                          ? buildSignature
                          : '';

                  return SettingsListTile(
                      leading: const Icon(Icons.app_registration),
                      title: S.of(context).settings_screen_mobile_version_title,
                      subtitle:
                          "$version; ${S.of(context).settings_screen_mobile_environment_label}: $env\n$buildSignature");
                },
              ),
              ...(context.watch<DebugViewModel?>()?.options ?? []).map((e) =>
                  SettingsListTile(
                      leading: const Icon(Icons.error),
                      title: e.name,
                      subtitle: e.description ?? '',
                      onTap: e.onTap)),
              if (context.watch<DebugViewModel?>()?.shouldShowTextStyles ==
                  true)
                const DebugTextStyles(),
            ]));
}
